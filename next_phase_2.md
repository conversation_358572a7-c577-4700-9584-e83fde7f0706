# Phase 2: Core Functionality Implementation

## Overview

Phase 2 focuses on implementing the core functionality for single-target flexible training, loss functions, and dataset handling. This phase builds upon the successful Phase 1 foundation to enable configurable single-curve model training with any supported curve type while maintaining simplicity and performance.

## Phase 2 Scope: Weeks 3-4 (According to General Refactoring Plan)

Based on the general refactoring guideline and current Phase 1 completion, Phase 2 will implement:

### 🎯 Core Phase 2 Objectives (Revised for Single-Target Focus)

1. **GeneralWellLogDataset for single-target flexible training**
2. **Configurable loss functions for different single curve types** 
3. **Enhanced training infrastructure supporting any single curve output**
4. **Configuration templates for common single-curve use cases**

## 📋 Detailed Phase 2 Tasks

### 2.1 Single-Target Flexible Dataset Implementation

**Priority: HIGH** - Essential for flexible single-curve training

#### Current Limitation (from refactor_general.md)
- **VpDataset** (`vp_model_improved.py:178-208`) assumes single target (AC/Vp curve only)
- Hardcoded input/output relationship
- No support for other single target curves (DEN, CNL, PE, etc.)

#### Implementation Tasks

1. **Create `core/dataset.py`**
   ```python
   class GeneralWellLogDataset(torch.utils.data.Dataset):
       def __init__(self, data_dict, input_curves, target_curve, 
                    normalizer, sequence_config):
           """
           Args:
               data_dict: Dict containing all curve data
               input_curves: List of input curve names
               target_curve: Single target curve name (AC, DEN, CNL, etc.)
               normalizer: GeneralDataNormalizer instance
               sequence_config: Sequence handling configuration
           """
   ```

2. **Features to Implement:**
   - Single-target configurable data loading and batching
   - Robust missing data handling strategies
   - Data quality validation and outlier detection
   - Flexible sequence length handling
   - Data augmentation for different curve types

3. **Backward Compatibility:**
   - Maintain `VpDataset` wrapper for existing code
   - Full compatibility with VpTransformer workflows

### 2.2 Flexible Single-Target Loss Functions

**Priority: MEDIUM** - Can be extended incrementally

#### Current Limitation (from refactor_general.md)
- **VpLoss** (`vp_model_improved.py:159-176`) has Vp-specific physical constraints only
- Single-curve loss but hardcoded for AC/Vp only
- No support for other curve types with different physics ranges

#### Implementation Tasks

1. **Create `core/loss_functions.py`**
   ```python
   class GeneralWellLogLoss(nn.Module):
       def __init__(self, target_curve_config, constraint_weight=1.0):
           """
           Args:
               target_curve_config: Configuration for the single target curve
               constraint_weight: Weight for physics constraint penalty
           """
   ```

2. **Loss Function Types:**
   - **Curve-Specific MSE Loss**: Base loss for single target curve
   - **Physics-Constrained Loss**: Curve-specific physical range penalties
   - **Curve-Specific Normalization**: Proper scaling for different curve ranges
   - **Robust Loss Options**: L1, Huber loss for outlier resistance

3. **Curve-Specific Constraints:**
   ```python
   PHYSICS_CONSTRAINTS = {
       'AC': (40, 400),     # μs/ft - sonic velocity range
       'DEN': (1.5, 3.0),   # g/cm³ - density range  
       'CNL': (0, 60),      # % - neutron porosity range
       'GR': (0, 200),      # API - gamma ray range
       'RLLD': (0.1, 1000)  # ohm-m - resistivity range
   }
   ```

### 2.3 Enhanced Single-Target Training Infrastructure

**Priority: MEDIUM** - Can reuse existing patterns

#### Current Limitation (from refactor_general.md)
- Fixed training loop for Vp prediction only (`vp_model_improved.py:244-320`)
- Hardcoded evaluation metrics (RMSE, R2) specific to AC/Vp
- No flexibility for other single curve targets

#### Implementation Tasks

1. **Create `core/training.py`**
   ```python
   class GeneralTrainingManager:
       def __init__(self, model_config, training_config, target_curve_config):
           """
           Flexible training manager for any single curve target
           """
       
       def create_curve_specific_loss(self, target_curve):
           """Create curve-specific loss function with appropriate constraints"""
       
       def setup_evaluation_metrics(self, target_curve):
           """Configure evaluation metrics for specific curve type"""
       
       def train_model(self, train_loader, val_loader, callbacks=None):
           """General training loop with configurable callbacks"""
   ```

2. **Training Features:**
   - Single-target flexible model training
   - Curve-specific learning rate scheduling
   - Early stopping with curve-appropriate validation
   - Training progress monitoring and logging
   - Checkpointing with curve-specific metadata

3. **Evaluation Metrics:**
   - Curve-specific RMSE, MAE, R²
   - Physics-based validity scoring per curve type
   - Curve-specific performance benchmarks
   - Training stability metrics

### 2.4 Configuration Templates Enhancement

**Priority: MEDIUM** - Build upon Phase 1 templates

#### Phase 1 Foundation
- Basic model templates created in `configs/models.py`
- 6 predefined templates (vp_prediction, multi_curve_basic, etc.)

#### Phase 2 Enhancement Tasks

1. **Training Configuration Templates**
   ```python
   TRAINING_TEMPLATES = {
       'vp_training': {
           'target_curve': 'AC',
           'loss_config': {'type': 'physics_constrained', 'constraint_weight': 1.0},
           'optimizer': {'type': 'adam', 'lr': 1e-4},
           'scheduler': {'type': 'plateau', 'patience': 25}
       },
       'density_training': {
           'target_curve': 'DEN',
           'loss_config': {'type': 'physics_constrained', 'constraint_weight': 0.5},
           'optimizer': {'type': 'adam', 'lr': 5e-5},
           'scheduler': {'type': 'plateau', 'patience': 30}
       },
       'neutron_training': {
           'target_curve': 'CNL',
           'loss_config': {'type': 'robust_loss', 'loss_type': 'huber'},
           'optimizer': {'type': 'adamw', 'lr': 8e-5},
           'scheduler': {'type': 'cosine', 'T_max': 100}
       }
   }
   ```

2. **Data Configuration Templates**
   ```python
   DATA_TEMPLATES = {
       'standard_sequence': {
           'total_length': 720,
           'effective_length': 640,
           'augmentation': True,
           'missing_data_handling': 'interpolation'
       },
       'long_sequence': {
           'total_length': 1440,
           'effective_length': 1280,
           'augmentation': False,
           'missing_data_handling': 'masking'
       }
   }
   ```

## 🏗️ Implementation Structure

### New Files to Create

```
vp_predictor/
├── core/
│   ├── dataset.py              # GeneralWellLogDataset (NEW - single-target)
│   ├── loss_functions.py       # Flexible single-target loss functions (NEW)
│   ├── training.py             # GeneralTrainingManager (NEW - single-target)
│   └── metrics.py              # Enhanced evaluation metrics (NEW)
├── configs/
│   ├── training.py             # Training configuration templates (NEW)
│   └── data.py                 # Data processing templates (NEW)
└── utils/
    ├── training_utils.py       # Training utilities and helpers (NEW)
    └── validation_utils.py     # Model validation utilities (NEW)
```

### Files to Extend

```
vp_predictor/
├── core/
│   ├── __init__.py            # Add new exports
│   └── transformer.py         # Add training integration
├── configs/
│   ├── __init__.py           # Add training config exports  
│   ├── models.py             # Enhance with training configs
│   └── validation.py         # Add training config validation
└── __init__.py               # Add Phase 2 exports
```

## 🎯 Success Criteria for Phase 2

### Functional Requirements
- [ ] Single-target flexible dataset can load and batch any supported curve
- [ ] Training manager can train models with any single output curve type
- [ ] Loss functions support curve-specific physics constraints appropriately
- [ ] Training templates work for common single-curve scenarios
- [ ] Backward compatibility maintained with existing VpTransformer training

### Technical Requirements  
- [ ] Training time comparable to current VpTransformer (minimal overhead)
- [ ] Memory usage similar to current single-target models
- [ ] Single-target loss convergence is stable for all curve types
- [ ] Curve-specific geological validity constraints are enforced
- [ ] Configuration system validates training parameters

### Integration Requirements
- [ ] New training components integrate with Phase 1 architecture
- [ ] API layer can trigger flexible single-curve training
- [ ] Configuration templates work with existing model templates
- [ ] Error handling covers various single-curve training scenarios

## 🚨 Risk Mitigation

### Technical Risks & Mitigations

1. **Curve-Specific Training Complexity**: Different curves may require different training approaches
   - **Mitigation**: Implement curve-specific configuration templates and extensive testing
   
2. **Configuration Complexity**: Flexible configuration may introduce validation issues
   - **Mitigation**: Implement comprehensive validation and provide clear examples

3. **Loss Function Design**: Different curves may require different loss function formulations
   - **Mitigation**: Implement physics-aware loss functions with curve-specific constraints

4. **Backward Compatibility**: New training infrastructure may break existing workflows
   - **Mitigation**: Maintain wrapper classes and comprehensive compatibility layers

## 📊 Progress Tracking

### Week 3 Goals
- [ ] GeneralWellLogDataset implementation complete (single-target flexible)
- [ ] Flexible single-target loss functions implemented and tested
- [ ] Training manager basic functionality working for different curve types
- [ ] Integration with Phase 1 components verified

### Week 4 Goals  
- [ ] Training configuration templates implemented for common curves
- [ ] End-to-end single-curve training working for multiple curve types
- [ ] Performance benchmarking vs current VpTransformer
- [ ] Documentation and examples created

## 🔄 Integration with Phase 1

Phase 2 leverages the successful Phase 1 foundation:

### Phase 1 Dependencies
- ✅ **GeneralDecoder**: Ready for single configurable outputs
- ✅ **GeneralDataNormalizer**: Can handle different single curve targets
- ✅ **Configuration System**: Provides curve definitions and validation
- ✅ **Model Templates**: Define input/output curve combinations (single output)

### Phase 2 Builds Upon
- **Templates → Training**: Convert model templates to training configurations
- **Configurations → Validation**: Extend validation to training parameters  
- **Architecture → Training**: Connect GeneralWellLogTransformer to flexible training pipeline
- **APIs → Training**: Enable flexible single-curve training through prediction APIs

## 🚀 Next Steps After Phase 2

Upon completion, Phase 2 will enable:

1. **Flexible single-curve model training** for any supported curve type
2. **Physics-aware optimization** with curve-specific geological constraints
3. **Stable single-target learning** with appropriate loss functions
4. **Template-driven training** for common single-curve use cases

This sets up **Phase 3: API Development** which will add:
- Advanced prediction APIs with curve flexibility
- Batch processing capabilities for different curve types
- Model evaluation frameworks
- Production deployment features

## 📋 Quick Implementation Checklist

### Pre-Phase 2 Verification
- [ ] Phase 1 test suite passes
- [ ] All Phase 1 components are functional
- [ ] Configuration system is working
- [ ] Backward compatibility is maintained

### Phase 2 Implementation Order
1. **GeneralWellLogDataset (Single-Target Flexible)** (Week 3, Days 1-3)
2. **Flexible Single-Target Loss Functions** (Week 3, Days 4-5)
3. **GeneralTrainingManager (Single-Target)** (Week 4, Days 1-3)  
4. **Configuration Integration** (Week 4, Days 4-5)

---

**Phase 2 transforms the system from a general architecture to a trainable single-curve flexible prediction platform, enabling the full potential of the GeneralWellLogTransformer while maintaining simplicity and performance.**