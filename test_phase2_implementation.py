"""
Comprehensive test suite for Phase 2 implementation

This test verifies that all Phase 2 components integrate properly with Phase 1
components and provide the expected single-target flexible training functionality.
"""

import sys
import os
import numpy as np
import torch
import logging
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all Phase 2 components can be imported correctly"""
    logger.info("=== Testing Phase 2 Imports ===")
    
    try:
        # Test core Phase 2 imports
        from vp_predictor.core import (
            GeneralWellLogDataset, VpDatasetCompatible, create_dataset_from_template,
            GeneralWellLogLoss, CurveSpecificLossFactory, VpLossCompatible,
            GeneralTrainingManager, create_vp_trainer, create_density_trainer
        )
        logger.info("✓ Core training components imported successfully")
        
        # Test configuration imports
        from vp_predictor.configs import (
            TRAINING_TEMPLATES, DATA_TEMPLATES, INTEGRATED_TEMPLATES,
            get_training_template, get_data_template, get_integrated_template,
            create_custom_training_config, validate_training_config
        )
        logger.info("✓ Training configuration components imported successfully")
        
        # Test Phase 1 integration
        from vp_predictor.core import (
            GeneralWellLogTransformer, GeneralDecoder, GeneralDataNormalizer,
            GWLT_VpPrediction
        )
        logger.info("✓ Phase 1 components still accessible")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import failed: {e}")
        return False

def test_configuration_templates():
    """Test training configuration templates"""
    logger.info("=== Testing Configuration Templates ===")
    
    try:
        from vp_predictor.configs import (
            get_training_template, get_data_template, validate_training_config,
            list_available_templates
        )
        
        # Test template availability
        templates = list_available_templates()
        logger.info(f"Available templates: {templates}")
        
        # Test specific training templates
        vp_config = get_training_template('vp_training')
        assert vp_config['target_curve'] == 'AC'
        assert 'learning_rate' in vp_config
        logger.info("✓ VP training template loaded correctly")
        
        density_config = get_training_template('density_training')
        assert density_config['target_curve'] == 'DEN'
        logger.info("✓ Density training template loaded correctly")
        
        # Test data template
        data_config = get_data_template('standard_sequence')
        assert data_config['total_length'] == 720
        assert data_config['effective_length'] == 640
        logger.info("✓ Data template loaded correctly")
        
        # Test validation
        warnings = validate_training_config(vp_config)
        logger.info(f"Validation warnings: {len(warnings)}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration test failed: {e}")
        return False

def test_loss_functions():
    """Test loss function components"""
    logger.info("=== Testing Loss Functions ===")
    
    try:
        from vp_predictor.core import (
            GeneralWellLogLoss, CurveSpecificLossFactory,
            create_vp_loss, create_density_loss
        )
        
        # Test GeneralWellLogLoss creation
        vp_loss = GeneralWellLogLoss('AC', constraint_weight=1.0)
        logger.info("✓ GeneralWellLogLoss created for AC curve")
        
        # Test factory creation
        density_loss = CurveSpecificLossFactory.create_loss('DEN')
        logger.info("✓ Factory-created loss for DEN curve")
        
        # Test convenience functions
        vp_loss_conv = create_vp_loss()
        density_loss_conv = create_density_loss()
        logger.info("✓ Convenience loss functions work")
        
        # Test loss computation with dummy data
        batch_size, seq_len = 2, 640
        predictions = torch.randn(batch_size, 1, seq_len)
        targets = torch.randn(batch_size, 1, seq_len)
        
        loss_components = vp_loss(predictions, targets)
        assert 'total_loss' in loss_components
        assert 'base_loss' in loss_components
        logger.info("✓ Loss computation works correctly")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Loss function test failed: {e}")
        return False

def create_dummy_data():
    """Create dummy well log data for testing"""
    num_samples = 1000
    seq_len = 720
    
    # Create realistic well log curves
    data_dict = {}
    
    # Gamma Ray: 20-150 API units
    data_dict['GR'] = np.random.uniform(20, 150, num_samples * seq_len)
    
    # Neutron: 0-40%
    data_dict['CNL'] = np.random.uniform(0, 40, num_samples * seq_len)
    
    # Density: 2.0-2.8 g/cm3
    data_dict['DEN'] = np.random.uniform(2.0, 2.8, num_samples * seq_len)
    
    # Resistivity: 1-100 ohm-m (log-distributed)
    data_dict['RLLD'] = np.random.lognormal(1, 1, num_samples * seq_len)
    
    # Acoustic: 60-120 us/ft (correlated with porosity)
    porosity_factor = (data_dict['CNL'] / 40.0)  # Normalized porosity
    data_dict['AC'] = 60 + porosity_factor * 60 + np.random.normal(0, 5, num_samples * seq_len)
    data_dict['AC'] = np.clip(data_dict['AC'], 40, 400)  # Clip to physical range
    
    return data_dict

def test_dataset():
    """Test dataset components"""
    logger.info("=== Testing Dataset Components ===")
    
    try:
        from vp_predictor.core import (
            GeneralWellLogDataset, VpDatasetCompatible,
            GeneralDataNormalizer
        )
        from vp_predictor.configs import get_data_template
        
        # Create dummy data
        data_dict = create_dummy_data()
        logger.info("✓ Dummy data created")
        
        # Test GeneralDataNormalizer
        normalizer = GeneralDataNormalizer(['GR', 'CNL', 'DEN', 'RLLD'], ['AC'])
        logger.info("✓ GeneralDataNormalizer created")
        
        # Test GeneralWellLogDataset for VP prediction
        vp_dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            target_curve='AC',
            normalizer=normalizer,
            sequence_config=get_data_template('standard_sequence')
        )
        logger.info(f"✓ VP dataset created with {len(vp_dataset)} samples")
        
        # Test sample retrieval
        inputs, targets = vp_dataset[0]
        assert inputs.shape == (4, 640)  # 4 input curves, 640 length
        assert targets.shape == (1, 640)  # 1 output curve, 640 length
        logger.info("✓ Sample retrieval works correctly")
        
        # Test density dataset
        density_dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=['GR', 'CNL', 'AC', 'RLLD'],
            target_curve='DEN',
            normalizer=None,  # Test without normalizer
            sequence_config=get_data_template('standard_sequence')
        )
        logger.info(f"✓ Density dataset created with {len(density_dataset)} samples")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Dataset test failed: {e}")
        return False

def test_training_manager():
    """Test training manager components"""
    logger.info("=== Testing Training Manager ===")
    
    try:
        from vp_predictor.core import (
            GeneralWellLogTransformer, GeneralWellLogDataset, 
            GeneralTrainingManager, GeneralDataNormalizer,
            create_vp_trainer
        )
        from vp_predictor.configs import get_training_template, get_data_template
        
        # Create dummy data and datasets
        data_dict = create_dummy_data()
        normalizer = GeneralDataNormalizer(['GR', 'CNL', 'DEN', 'RLLD'], ['AC'])
        
        # Split data for train/val
        train_size = int(0.8 * len(data_dict['AC']) // 720)
        val_start = train_size * 720
        
        train_data = {k: v[:val_start] for k, v in data_dict.items()}
        val_data = {k: v[val_start:] for k, v in data_dict.items()}
        
        train_dataset = GeneralWellLogDataset(
            train_data, ['GR', 'CNL', 'DEN', 'RLLD'], 'AC',
            normalizer, get_data_template('standard_sequence')
        )
        
        val_dataset = GeneralWellLogDataset(
            val_data, ['GR', 'CNL', 'DEN', 'RLLD'], 'AC',
            normalizer, get_data_template('standard_sequence')
        )
        
        logger.info(f"✓ Train dataset: {len(train_dataset)} samples")
        logger.info(f"✓ Val dataset: {len(val_dataset)} samples")
        
        # Create model
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        logger.info("✓ Model created from template")
        
        # Test GeneralTrainingManager creation
        training_config = get_training_template('fast_vp_training')  # Use fast config for testing
        training_config['max_epochs'] = 2  # Very short for testing
        training_config['batch_size'] = 4
        
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )
        logger.info("✓ GeneralTrainingManager created")
        
        # Test training info
        info = trainer.get_training_info()
        assert info['target_curve'] == 'AC'
        logger.info("✓ Training info retrieval works")
        
        # Test convenience function
        vp_trainer = create_vp_trainer(model, train_dataset, val_dataset)
        logger.info("✓ Convenience trainer creation works")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Training manager test failed: {e}")
        return False

def test_integration():
    """Test full integration of Phase 1 and Phase 2 components"""
    logger.info("=== Testing Phase 1 + Phase 2 Integration ===")
    
    try:
        from vp_predictor import (
            # Phase 1 components
            GeneralWellLogTransformer, GeneralDataNormalizer,
            get_model_template,
            
            # Phase 2 components
            GeneralWellLogDataset, GeneralTrainingManager,
            get_training_template, get_data_template,
            create_vp_loss
        )
        
        # Create complete training pipeline
        logger.info("Setting up complete training pipeline...")
        
        # 1. Get configurations
        model_config = get_model_template('vp_prediction')
        training_config = get_training_template('fast_vp_training')
        training_config['max_epochs'] = 1  # Single epoch for testing
        training_config['batch_size'] = 2
        data_config = get_data_template('standard_sequence')
        
        # 2. Create data
        data_dict = create_dummy_data()
        
        # 3. Create components
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        normalizer = GeneralDataNormalizer(
            model_config['input_curves'], 
            model_config['output_curves']
        )
        
        dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],  # Single target
            normalizer=normalizer,
            sequence_config=data_config
        )
        
        # 4. Create trainer
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=dataset,
            val_dataset=None,  # No validation for quick test
            training_config=training_config
        )
        
        logger.info("✓ Complete pipeline created successfully")
        
        # Test that all components work together
        info = trainer.get_training_info()
        assert info['target_curve'] == 'AC'
        assert len(info['dataset_info']['input_curves']) == 4
        
        # Test single training step
        train_metrics = trainer.train_epoch()
        assert 'total_loss' in train_metrics
        assert train_metrics['total_loss'] > 0
        
        logger.info("✓ Training step executed successfully")
        logger.info(f"  Train loss: {train_metrics['total_loss']:.6f}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Integration test failed: {e}")
        return False

def test_backward_compatibility():
    """Test backward compatibility with VpTransformer"""
    logger.info("=== Testing Backward Compatibility ===")
    
    try:
        from vp_predictor.core import VpDatasetCompatible, VpLossCompatible
        
        # Create dummy data in old format
        batch_size = 10
        seq_len = 720
        
        # Old VpDataset format: input [N, 4, seqlen], target [N, seqlen]
        input_data = np.random.randn(batch_size, 4, seq_len)
        target_data = np.random.uniform(60, 120, (batch_size, seq_len))
        
        # Test VpDatasetCompatible
        compat_dataset = VpDatasetCompatible(
            input_data=input_data,
            target_data=target_data,
            normalizer=None,
            total_seqlen=720,
            effect_seqlen=640,
            transform=False
        )
        
        logger.info(f"✓ VpDatasetCompatible created with {len(compat_dataset)} samples")
        
        # Test sample retrieval
        inputs, targets = compat_dataset[0]
        assert inputs.shape == (4, 640)
        assert targets.shape == (1, 640)
        logger.info("✓ Compatible dataset sample retrieval works")
        
        # Test VpLossCompatible
        compat_loss = VpLossCompatible(constraint_weight=1.0)
        
        # Test loss computation
        pred_vp = torch.randn(2, 1, 640)
        target_vp = torch.randn(2, 1, 640)
        
        loss_value = compat_loss(pred_vp, target_vp)
        assert isinstance(loss_value, torch.Tensor)
        assert loss_value.dim() == 0  # Scalar loss
        logger.info("✓ Compatible loss computation works")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Backward compatibility test failed: {e}")
        return False

def test_different_curves():
    """Test training with different target curves"""
    logger.info("=== Testing Different Target Curves ===")
    
    try:
        from vp_predictor.core import (
            GeneralWellLogDataset, GeneralTrainingManager,
            GeneralWellLogTransformer, GeneralDataNormalizer,
            create_density_loss, create_neutron_loss
        )
        from vp_predictor.configs import get_training_template
        
        # Create data
        data_dict = create_dummy_data()
        
        # Test 1: Density prediction
        logger.info("Testing density prediction...")
        
        density_normalizer = GeneralDataNormalizer(['GR', 'CNL', 'AC', 'RLLD'], ['DEN'])
        density_dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=['GR', 'CNL', 'AC', 'RLLD'],
            target_curve='DEN',
            normalizer=density_normalizer
        )
        
        density_model = GeneralWellLogTransformer(
            input_curves=['GR', 'CNL', 'AC', 'RLLD'],
            output_curves=['DEN']
        )
        
        density_config = get_training_template('density_training')
        density_config['max_epochs'] = 1
        density_config['batch_size'] = 2
        
        density_trainer = GeneralTrainingManager(
            model=density_model,
            train_dataset=density_dataset,
            training_config=density_config
        )
        
        density_info = density_trainer.get_training_info()
        assert density_info['target_curve'] == 'DEN'
        logger.info("✓ Density training setup successful")
        
        # Test 2: Neutron prediction
        logger.info("Testing neutron prediction...")
        
        neutron_dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=['GR', 'DEN', 'AC', 'RLLD'],
            target_curve='CNL'
        )
        
        neutron_loss = create_neutron_loss(robust=True)
        logger.info("✓ Neutron training setup successful")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Different curves test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all Phase 2 tests"""
    logger.info("🚀 Starting Phase 2 Comprehensive Test Suite")
    logger.info("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Templates", test_configuration_templates),
        ("Loss Functions", test_loss_functions),
        ("Dataset Components", test_dataset),
        ("Training Manager", test_training_manager),
        ("Phase 1+2 Integration", test_integration),
        ("Backward Compatibility", test_backward_compatibility),
        ("Different Target Curves", test_different_curves)
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name}...")
        try:
            success = test_func()
            results[test_name] = success
            if success:
                passed_tests += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            results[test_name] = False
            logger.error(f"💥 {test_name} CRASHED: {e}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 PHASE 2 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{test_name:<25} {status}")
    
    logger.info("-" * 60)
    logger.info(f"Total Tests: {total_tests}")
    logger.info(f"Passed: {passed_tests}")
    logger.info(f"Failed: {total_tests - passed_tests}")
    logger.info(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! Phase 2 implementation is working correctly.")
        return True
    else:
        logger.error(f"⚠️  {total_tests - passed_tests} tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    # Add current directory to Python path for imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)