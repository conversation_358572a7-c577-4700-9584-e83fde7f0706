"""
Test script to demonstrate GPU/CPU device detection functionality
"""
import torch
from utils import get_device, load_checkpoint

def test_device_detection():
    """Test the device detection functionality"""
    print("=== Device Detection Test ===")
    
    # Test automatic device detection
    print("\n1. Automatic device detection:")
    device = get_device()
    print(f"Selected device: {device}")
    
    # Test specific GPU device (if available)
    if torch.cuda.is_available():
        print("\n2. Specific GPU device selection:")
        for i in range(torch.cuda.device_count()):
            device_i = get_device(i)
            print(f"GPU {i}: {device_i}")
    else:
        print("\n2. GPU not available, using CPU")
    
    # Test checkpoint loading with different devices
    print("\n3. Checkpoint loading test:")
    checkpoint_path = "../result_base_normal/best_model.pth"
    
    try:
        # Test loading with auto-detection
        print("Loading with auto-detection...")
        model_dict, epoch, loss = load_checkpoint(checkpoint_path)
        print(f"✓ Successfully loaded checkpoint (epoch: {epoch}, loss: {loss:.6f})")
        
        # Test loading with specific device
        print("Loading with specific device...")
        device = torch.device('cpu')  # Force CPU for compatibility
        model_dict, epoch, loss = load_checkpoint(checkpoint_path, device)
        print(f"✓ Successfully loaded checkpoint to {device} (epoch: {epoch}, loss: {loss:.6f})")
        
    except Exception as e:
        print(f"✗ Error loading checkpoint: {e}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_device_detection()
