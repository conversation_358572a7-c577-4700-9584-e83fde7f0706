"""
Test script for Vp (Sonic Velocity) prediction using transformer model
Supports both LAS files and HDF5 files (A1.hdf5, A2.hdf5)
"""
import os
import sys
import argparse
import time
import numpy as np
import h5py
import torch
from torch.utils.data import Dataset, DataLoader

# Import existing modules
from utils import get_device, load_checkpoint, cal_RMSE, cal_R2
from model import MWLT_Small, MWLT_Base, MWLT_Large
from las_processor import LASProcessor

class VpPredictionDataset(Dataset):
    """
    Dataset for Vp prediction from well log data
    """
    
    def __init__(self, data_files: list, processor: LASProcessor, total_seqlen: int = 720, effect_seqlen: int = 640):
        self.data_files = data_files
        self.processor = processor
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        
    def __len__(self):
        return len(self.data_files)
    
    def __getitem__(self, idx):
        file_path = self.data_files[idx]
        file_name = os.path.basename(file_path)
        
        # Process the file based on its extension
        if file_path.endswith('.hdf5'):
            curves = self.processor.process_hdf5_to_las_format(file_path)
        elif file_path.endswith('.las'):
            curves = self.processor.read_las_file(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path}")
        
        # Prepare data for prediction
        input_features, target = self.processor.prepare_for_prediction(curves, self.total_seqlen)
        
        # Crop to effect sequence length
        input_features = input_features[:, :self.effect_seqlen]
        target = target[:, :self.effect_seqlen]
        
        # Convert to tensors
        input_tensor = torch.from_numpy(input_features).type(torch.FloatTensor)
        target_tensor = torch.from_numpy(target).type(torch.FloatTensor)
        
        return input_tensor, target_tensor, file_name

def predict_vp(args):
    """
    Main function for Vp prediction
    """
    print("=== Vp (Sonic Velocity) Prediction Test ===")
    
    # Device setup
    device = get_device(int(args.device))
    
    # Create output directory
    if not os.path.exists(args.save_path):
        os.makedirs(args.save_path)
    
    # Initialize LAS processor
    processor = LASProcessor()
    
    # Prepare data files
    data_files = []
    if args.input_file:
        # Single file prediction
        if os.path.exists(args.input_file):
            data_files = [args.input_file]
        else:
            print(f"Error: Input file {args.input_file} not found!")
            return
    else:
        # Use A1.hdf5 and A2.hdf5 by default
        for file_name in ['../A1.hdf5', '../A2.hdf5']:
            if os.path.exists(file_name):
                data_files.append(file_name)
        
        if not data_files:
            print("Error: No A1.hdf5 or A2.hdf5 files found!")
            return
    
    print(f"Processing {len(data_files)} file(s): {data_files}")
    
    # Create dataset and dataloader
    dataset = VpPredictionDataset(data_files, processor, args.total_seqlen, args.effect_seqlen)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)
    
    # Load model
    print(f"\nLoading model: {args.model_type}")
    if args.model_type == "small":
        model = MWLT_Small(in_channels=len(processor.required_curves), out_channels=1,
                          feature_num=args.feature_num, use_pe=args.use_pe, 
                          drop=args.drop, attn_drop=args.attn_drop, position_drop=args.position_drop)
    elif args.model_type == "base":
        model = MWLT_Base(in_channels=len(processor.required_curves), out_channels=1,
                         feature_num=args.feature_num, use_pe=args.use_pe,
                         drop=args.drop, attn_drop=args.attn_drop, position_drop=args.position_drop)
    elif args.model_type == "large":
        model = MWLT_Large(in_channels=len(processor.required_curves), out_channels=1,
                          feature_num=args.feature_num, use_pe=args.use_pe,
                          drop=args.drop, attn_drop=args.attn_drop, position_drop=args.position_drop)
    
    model = model.to(device)
    
    # Load checkpoint
    print(f"Loading checkpoint: {args.checkpoint_path}")
    model_dict, epoch, loss = load_checkpoint(args.checkpoint_path, device)
    model.load_state_dict(model_dict)
    model.eval()
    
    print(f"Model loaded successfully (epoch: {epoch}, loss: {loss:.6f})")
    print(f"Model is on device: {next(model.parameters()).device}")
    
    # Prediction loop
    total_inference_time = 0
    results = []
    
    print(f"\nStarting Vp prediction on {device}")
    print("-" * 60)
    
    with torch.no_grad():
        for step, (input_features, target, file_name) in enumerate(dataloader):
            start_time = time.time()
            
            # Move to device
            input_features = input_features.to(device)
            target = target.to(device)
            
            # Perform prediction
            inference_start = time.time()
            predicted_vp = model(input_features)
            inference_time = time.time() - inference_start
            total_inference_time += inference_time
            
            # Move to CPU for processing
            target_np = target.cpu().numpy().squeeze()
            predicted_np = predicted_vp.cpu().numpy().squeeze()
            
            # Calculate metrics
            rmse = cal_RMSE(predicted_np, target_np)
            r2 = cal_R2(predicted_np, target_np)
            
            # Save results
            output_file = os.path.join(args.save_path, f"vp_pred_{file_name[0].replace('.hdf5', '').replace('.las', '')}.hdf5")
            with h5py.File(output_file, 'w') as f:
                f.create_dataset('real_vp', data=target_np)
                f.create_dataset('predicted_vp', data=predicted_np)
                f.create_dataset('input_features', data=input_features.cpu().numpy().squeeze())
                
                # Add metadata
                f.attrs['rmse'] = rmse
                f.attrs['r2'] = r2
                f.attrs['source_file'] = file_name[0]
                f.attrs['model_type'] = args.model_type
                f.attrs['inference_time'] = inference_time
            
            # Store results for summary
            results.append({
                'file': file_name[0],
                'rmse': rmse,
                'r2': r2,
                'inference_time': inference_time,
                'output_file': output_file
            })
            
            step_time = time.time() - start_time
            print(f"File: {file_name[0]:<15} | RMSE: {rmse:.4f} | R²: {r2:.4f} | Time: {step_time:.4f}s")
    
    # Print summary
    print("-" * 60)
    print("=== Vp Prediction Summary ===")
    print(f"Device used: {device}")
    print(f"Model type: {args.model_type}")
    print(f"Total files processed: {len(results)}")
    print(f"Total inference time: {total_inference_time:.4f}s")
    print(f"Average inference time: {total_inference_time/len(results):.4f}s")
    
    if results:
        avg_rmse = np.mean([r['rmse'] for r in results])
        avg_r2 = np.mean([r['r2'] for r in results])
        print(f"Average RMSE: {avg_rmse:.4f}")
        print(f"Average R²: {avg_r2:.4f}")
    
    print(f"\nResults saved to: {args.save_path}")
    for result in results:
        print(f"  {result['output_file']}")
    
    if torch.cuda.is_available() and device.type == 'cuda':
        print(f"GPU memory allocated: {torch.cuda.memory_allocated(device)/1024**2:.1f} MB")

def main():
    parser = argparse.ArgumentParser(description="Predict Vp (Sonic Velocity) using transformer model")
    
    # Model parameters
    parser.add_argument("--device", type=str, default="0", help="GPU device ID")
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"],
                       help="Model size")
    parser.add_argument("--checkpoint_path", type=str, default="../result_base_normal/best_model.pth",
                       help="Path to model checkpoint")
    
    # Data parameters
    parser.add_argument("--input_file", type=str, default=None,
                       help="Specific input file (LAS or HDF5). If not provided, uses A1.hdf5 and A2.hdf5")
    parser.add_argument("--save_path", type=str, default="../vp_prediction_results",
                       help="Output directory for results")
    
    # Model configuration
    parser.add_argument("--total_seqlen", type=int, default=720, help="Total sequence length")
    parser.add_argument("--effect_seqlen", type=int, default=640, help="Effective sequence length")
    parser.add_argument("--feature_num", type=int, default=64, help="Feature dimensions")
    parser.add_argument("--use_pe", type=bool, default=True, help="Use position embedding")
    parser.add_argument("--drop", type=float, default=0.1, help="Dropout rate")
    parser.add_argument("--attn_drop", type=float, default=0.1, help="Attention dropout rate")
    parser.add_argument("--position_drop", type=float, default=0.1, help="Position dropout rate")
    
    args = parser.parse_args()
    predict_vp(args)

if __name__ == "__main__":
    main()
