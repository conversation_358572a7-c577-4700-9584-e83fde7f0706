"""
Improved Vp training script with proper normalization and architecture
Addresses the fundamental issues causing poor performance
"""
import os
import argparse
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py
from sklearn.model_selection import train_test_split

from vp_model_improved import MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, VpDataNormalizer, VpLoss
from utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from las_processor import LASProcessor

class VpDataset(torch.utils.data.Dataset):
    """
    Improved dataset for Vp prediction with proper normalization
    """
    def __init__(self, input_data, target_data, normalizer, total_seqlen=720, effect_seqlen=640, transform=False):
        self.input_data = input_data
        self.target_data = target_data
        self.normalizer = normalizer
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        self.transform = transform
        
    def __len__(self):
        return len(self.input_data)
    
    def __getitem__(self, idx):
        # Get input curves and target
        inputs = self.input_data[idx]  # Shape: [4, 720] for GR, CNL, DEN, RLLD
        target = self.target_data[idx]  # Shape: [720] for AC

        # Apply data augmentation if requested
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]

        # UPDATED: Normalize target values for training using VpDataNormalizer
        # This ensures consistency with the new VpDecoder architecture
        target_tensor = torch.FloatTensor(target)
        target_normalized = self.normalizer.normalize_vp(target_tensor)

        return torch.FloatTensor(inputs), target_normalized.unsqueeze(0)

def find_data_files():
    """
    Auto-detect the location of A1.hdf5 and A2.hdf5 files
    """
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]

    found_files = []
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')

        if os.path.exists(a1_path) and os.path.exists(a2_path):
            found_files = [a1_path, a2_path]
            print(f"✅ Found data files in: {os.path.abspath(base_path)}")
            break

    if not found_files:
        print("❌ Could not find A1.hdf5 and A2.hdf5 files!")
        print("Searched in:")
        for path in possible_paths:
            print(f"  - {os.path.abspath(path)}")
        print("\nPlease ensure A1.hdf5 and A2.hdf5 are in one of these locations.")

    return found_files

def create_improved_vp_data():
    """
    Create improved training data with proper normalization and augmentation
    """
    processor = LASProcessor()
    normalizer = VpDataNormalizer()

    print("Creating improved Vp training data...")

    # Auto-detect data file locations
    input_files = find_data_files()
    if not input_files:
        return np.array([]), np.array([])
    all_inputs = []
    all_targets = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"Processing {file_path}...")
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Create multiple overlapping windows for data augmentation
        sequence_length = 720
        step_size = 360  # 50% overlap
        
        data_length = len(curves['AC'])
        num_windows = max(1, (data_length - sequence_length) // step_size + 1)
        
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + sequence_length
            
            if end_idx > data_length:
                start_idx = data_length - sequence_length
                end_idx = data_length
            
            # Extract window
            window_curves = {}
            for curve_name, data in curves.items():
                window_curves[curve_name] = data[start_idx:end_idx]
            
            # Normalize inputs
            input_features = []
            for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
                if curve_name in window_curves:
                    data = torch.FloatTensor(window_curves[curve_name])
                    if curve_name == 'RLLD':
                        # Log transform for resistivity
                        data = torch.log10(torch.clamp(data, min=0.1))
                        normalized = (data - 1.0) / 2.0
                    else:
                        stats = normalizer.input_stats[curve_name]
                        normalized = (data - stats['mean']) / stats['std']
                        normalized = torch.clamp(normalized, -3, 3) / 3
                    input_features.append(normalized.numpy())
                else:
                    input_features.append(np.zeros(sequence_length))
            
            # Target (AC) - keep in original units for now
            target = window_curves.get('AC', np.zeros(sequence_length))
            
            all_inputs.append(np.array(input_features))
            all_targets.append(target)
    
    print(f"Created {len(all_inputs)} training samples")
    return np.array(all_inputs), np.array(all_targets)

def train_improved_vp_model():
    """
    Train improved Vp model with proper architecture and normalization
    """
    # Configuration
    config = {
        'model_type': 'base',
        'batch_size': 8,
        'learning_rate': 1e-4,
        'epochs': 200,
        'patience': 50,
        'device': 0,
        'save_path': os.path.join('..', 'vp_improved_training')
    }
    
    # Setup
    device = get_device(config['device'])
    os.makedirs(config['save_path'], exist_ok=True)
    
    # Create improved training data
    input_data, target_data = create_improved_vp_data()
    
    if len(input_data) == 0:
        print("Error: No training data created!")
        return
    
    # Split data
    train_inputs, val_inputs, train_targets, val_targets = train_test_split(
        input_data, target_data, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(train_inputs)}")
    print(f"Validation samples: {len(val_inputs)}")
    
    # Create datasets
    normalizer = VpDataNormalizer()
    train_dataset = VpDataset(train_inputs, train_targets, normalizer, transform=True)
    val_dataset = VpDataset(val_inputs, val_targets, normalizer, transform=False)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
    
    # Create improved model
    if config['model_type'] == 'small':
        model = MWLT_Vp_Small()
    elif config['model_type'] == 'base':
        model = MWLT_Vp_Base()
    else:
        model = MWLT_Vp_Large()
    
    model = model.to(device)
    
    # Setup training
    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])
    criterion = VpLoss()
    early_stopping = EarlyStopping(patience=config['patience'], 
                                  path=os.path.join(config['save_path'], 'best_vp_improved_model.pth'))
    
    print(f"\nStarting improved Vp training on {device}")
    print(f"Model: {config['model_type']}, Samples: {len(train_inputs)}")
    
    # Training loop
    for epoch in range(1, config['epochs'] + 1):
        # Training
        model.train()
        train_loss = 0.0
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_predictions = []
        val_actuals = []
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
                
                val_predictions.extend(outputs.cpu().numpy().flatten())
                val_actuals.extend(targets.cpu().numpy().flatten())
        
        val_loss /= len(val_loader)
        
        # Calculate metrics
        val_rmse = cal_RMSE(np.array(val_predictions), np.array(val_actuals))
        val_r2 = cal_R2(np.array(val_predictions), np.array(val_actuals))
        
        print(f"Epoch {epoch:3d}: Train Loss: {train_loss:.6f}, "
              f"Val Loss: {val_loss:.6f}, RMSE: {val_rmse:.2f}, R²: {val_r2:.4f}")
        
        # Early stopping - save complete state dictionary
        state = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "loss": val_loss,
            "epoch": epoch,
            "rmse": val_rmse,
            "r2": val_r2
        }
        early_stopping(state, model)
        if early_stopping.early_stop:
            print(f"Early stopping at epoch {epoch}")
            break
    
    print(f"\nTraining completed! Best model saved to {config['save_path']}")
    return config['save_path']

if __name__ == "__main__":
    train_improved_vp_model()
