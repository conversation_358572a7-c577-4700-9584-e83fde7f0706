"""
General Training Manager for single-target flexible training

This module provides a flexible training infrastructure that can train models
for any single curve target while maintaining backward compatibility and providing
comprehensive training features.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
import logging
import time
import numpy as np
from pathlib import Path
import json

from .loss_functions import GeneralWellLogLoss, CurveSpecificLossFactory
from .dataset import GeneralWellLogDataset
from .transformer import GeneralWellLogTransformer
from ..configs.curves import CURVE_CONFIGURATIONS
from ..utils import get_device

logger = logging.getLogger(__name__)


def get_model_info(model):
    """Get basic model information"""
    try:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_class': model.__class__.__name__
        }
    except:
        return {'model_class': model.__class__.__name__}


class GeneralTrainingManager:
    """
    Flexible training manager for any single curve target
    
    This manager handles the complete training pipeline including model setup,
    loss function configuration, optimization, validation, and checkpointing
    for any supported single-target curve type.
    """
    
    def __init__(
        self,
        model: GeneralWellLogTransformer,
        train_dataset: GeneralWellLogDataset,
        val_dataset: Optional[GeneralWellLogDataset] = None,
        training_config: Optional[Dict[str, Any]] = None,
        device: Optional[torch.device] = None
    ):
        """
        Initialize GeneralTrainingManager
        
        Args:
            model: GeneralWellLogTransformer model to train
            train_dataset: Training dataset
            val_dataset: Optional validation dataset
            training_config: Training configuration parameters
            device: Target device for training
        """
        self.model = model
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.device = device or get_device()
        
        # Default training configuration
        default_config = {
            'learning_rate': 1e-4,
            'weight_decay': 1e-5,
            'batch_size': 8,
            'max_epochs': 200,
            'patience': 50,
            'optimizer': 'adam',
            'scheduler': 'plateau',
            'scheduler_params': {'patience': 25, 'factor': 0.5, 'min_lr': 1e-7},
            'loss_config': {'type': 'curve_specific'},
            'save_best_only': True,
            'save_frequency': 10,
            'validation_frequency': 1,
            'gradient_clipping': 1.0,
            'mixed_precision': False
        }
        
        self.config = {**default_config, **(training_config or {})}
        
        # Get target curve from dataset
        self.target_curve = train_dataset.target_curve
        
        # Initialize training components
        self._setup_training()
        
        # Training state
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rates': [],
            'epoch_times': []
        }
        
        logger.info(f"GeneralTrainingManager initialized for '{self.target_curve}' prediction")
        logger.info(f"  Model: {get_model_info(self.model)}")
        logger.info(f"  Training samples: {len(self.train_dataset)}")
        logger.info(f"  Validation samples: {len(self.val_dataset) if self.val_dataset else 'None'}")
        logger.info(f"  Device: {self.device}")
    
    def _setup_training(self):
        """Setup training components"""
        # Move model to device
        self.model = self.model.to(self.device)
        
        # Setup loss function
        self.loss_function = self._create_loss_function()
        
        # Setup optimizer
        self.optimizer = self._create_optimizer()
        
        # Setup scheduler
        self.scheduler = self._create_scheduler()
        
        # Setup data loaders
        self.train_loader = self._create_data_loader(self.train_dataset, shuffle=True)
        if self.val_dataset:
            self.val_loader = self._create_data_loader(self.val_dataset, shuffle=False)
        else:
            self.val_loader = None
        
        # Setup mixed precision if enabled
        if self.config['mixed_precision']:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
    
    def _create_loss_function(self) -> GeneralWellLogLoss:
        """Create curve-specific loss function"""
        loss_config = self.config.get('loss_config', {})
        
        if loss_config.get('type') == 'curve_specific':
            # Use curve-specific optimized configuration
            return CurveSpecificLossFactory.create_loss(
                self.target_curve,
                custom_config=loss_config.get('custom_params')
            )
        else:
            # Use custom configuration
            return GeneralWellLogLoss(
                target_curve=self.target_curve,
                **loss_config
            )
    
    def _create_optimizer(self) -> optim.Optimizer:
        """Create optimizer"""
        optimizer_name = self.config['optimizer'].lower()
        
        optimizer_params = {
            'params': self.model.parameters(),
            'lr': self.config['learning_rate'],
            'weight_decay': self.config['weight_decay']
        }
        
        if optimizer_name == 'adam':
            return optim.Adam(**optimizer_params)
        elif optimizer_name == 'adamw':
            return optim.AdamW(**optimizer_params)
        elif optimizer_name == 'sgd':
            optimizer_params['momentum'] = 0.9
            return optim.SGD(**optimizer_params)
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_name}")
    
    def _create_scheduler(self) -> Optional[object]:
        """Create learning rate scheduler"""
        scheduler_name = self.config.get('scheduler', '').lower()
        
        if scheduler_name == 'plateau':
            params = self.config.get('scheduler_params', {})
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                patience=params.get('patience', 25),
                factor=params.get('factor', 0.5),
                min_lr=params.get('min_lr', 1e-7),
                verbose=True
            )
        elif scheduler_name == 'cosine':
            params = self.config.get('scheduler_params', {})
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=params.get('T_max', 100),
                eta_min=params.get('eta_min', 1e-7)
            )
        elif scheduler_name == 'exponential':
            params = self.config.get('scheduler_params', {})
            return optim.lr_scheduler.ExponentialLR(
                self.optimizer,
                gamma=params.get('gamma', 0.95)
            )
        else:
            return None
    
    def _create_data_loader(self, dataset: GeneralWellLogDataset, shuffle: bool = True) -> DataLoader:
        """Create data loader"""
        return DataLoader(
            dataset,
            batch_size=self.config['batch_size'],
            shuffle=shuffle,
            num_workers=0,  # Set to 0 for Windows compatibility
            pin_memory=True if self.device.type == 'cuda' else False
        )
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        total_samples = 0
        loss_components = {}
        
        for batch_idx, (inputs, targets) in enumerate(self.train_loader):
            inputs = inputs.to(self.device)
            targets = targets.to(self.device)
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass with mixed precision if enabled
            if self.scaler:
                with torch.cuda.amp.autocast():
                    predictions = self.model(inputs)
                    loss_dict = self.loss_function(predictions, targets)
                    loss = loss_dict['total_loss']
            else:
                predictions = self.model(inputs)
                loss_dict = self.loss_function(predictions, targets)
                loss = loss_dict['total_loss']
            
            # Backward pass
            if self.scaler:
                self.scaler.scale(loss).backward()
                
                # Gradient clipping
                if self.config['gradient_clipping'] > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.config['gradient_clipping']
                    )
                
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                loss.backward()
                
                # Gradient clipping
                if self.config['gradient_clipping'] > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.config['gradient_clipping']
                    )
                
                self.optimizer.step()
            
            # Accumulate loss
            batch_size = inputs.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size
            
            # Accumulate loss components
            for key, value in loss_dict.items():
                if key not in loss_components:
                    loss_components[key] = 0.0
                if isinstance(value, torch.Tensor):
                    loss_components[key] += value.item() * batch_size
                else:
                    loss_components[key] += value * batch_size
        
        # Average losses
        avg_loss = total_loss / total_samples
        for key in loss_components:
            loss_components[key] = loss_components[key] / total_samples
        
        return {'total_loss': avg_loss, **loss_components}
    
    def validate_epoch(self) -> Dict[str, float]:
        """Validate for one epoch"""
        if not self.val_loader:
            return {}
        
        self.model.eval()
        total_loss = 0.0
        total_samples = 0
        loss_components = {}
        
        with torch.no_grad():
            for inputs, targets in self.val_loader:
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                # Forward pass
                if self.scaler:
                    with torch.cuda.amp.autocast():
                        predictions = self.model(inputs)
                        loss_dict = self.loss_function(predictions, targets)
                        loss = loss_dict['total_loss']
                else:
                    predictions = self.model(inputs)
                    loss_dict = self.loss_function(predictions, targets)
                    loss = loss_dict['total_loss']
                
                # Accumulate loss
                batch_size = inputs.size(0)
                total_loss += loss.item() * batch_size
                total_samples += batch_size
                
                # Accumulate loss components
                for key, value in loss_dict.items():
                    if key not in loss_components:
                        loss_components[key] = 0.0
                    if isinstance(value, torch.Tensor):
                        loss_components[key] += value.item() * batch_size
                    else:
                        loss_components[key] += value * batch_size
        
        # Average losses
        avg_loss = total_loss / total_samples
        for key in loss_components:
            loss_components[key] = loss_components[key] / total_samples
        
        return {'total_loss': avg_loss, **loss_components}
    
    def train_model(
        self,
        save_path: Optional[str] = None,
        callbacks: Optional[List[Callable]] = None
    ) -> Dict[str, Any]:
        """
        Train the model
        
        Args:
            save_path: Path to save model checkpoints
            callbacks: Optional list of callback functions
            
        Returns:
            Training history and final metrics
        """
        logger.info("Starting training...")
        logger.info(f"Configuration: {self.config}")
        
        start_time = time.time()
        best_model_path = None
        
        try:
            for epoch in range(self.config['max_epochs']):
                self.epoch = epoch
                epoch_start = time.time()
                
                # Training phase
                train_metrics = self.train_epoch()
                
                # Validation phase
                val_metrics = {}
                if epoch % self.config['validation_frequency'] == 0:
                    val_metrics = self.validate_epoch()
                
                # Learning rate scheduling
                if self.scheduler:
                    if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                        if val_metrics:
                            self.scheduler.step(val_metrics['total_loss'])
                        else:
                            self.scheduler.step(train_metrics['total_loss'])
                    else:
                        self.scheduler.step()
                
                # Record metrics
                epoch_time = time.time() - epoch_start
                current_lr = self.optimizer.param_groups[0]['lr']
                
                self.training_history['train_loss'].append(train_metrics['total_loss'])
                self.training_history['learning_rates'].append(current_lr)
                self.training_history['epoch_times'].append(epoch_time)
                
                if val_metrics:
                    self.training_history['val_loss'].append(val_metrics['total_loss'])
                    val_loss = val_metrics['total_loss']
                else:
                    val_loss = train_metrics['total_loss']
                
                # Early stopping and best model saving
                is_best = val_loss < self.best_val_loss
                if is_best:
                    self.best_val_loss = val_loss
                    self.patience_counter = 0
                    
                    # Save best model
                    if save_path and self.config['save_best_only']:
                        best_model_path = self._save_checkpoint(save_path, epoch, is_best=True)
                else:
                    self.patience_counter += 1
                
                # Regular checkpoint saving
                if save_path and not self.config['save_best_only'] and epoch % self.config['save_frequency'] == 0:
                    self._save_checkpoint(save_path, epoch, is_best=False)
                
                # Logging
                log_msg = f"Epoch {epoch+1:3d}/{self.config['max_epochs']} | "
                log_msg += f"Train Loss: {train_metrics['total_loss']:.6f} | "
                if val_metrics:
                    log_msg += f"Val Loss: {val_metrics['total_loss']:.6f} | "
                log_msg += f"LR: {current_lr:.2e} | Time: {epoch_time:.1f}s"
                if is_best:
                    log_msg += " | BEST"
                
                logger.info(log_msg)
                
                # Run callbacks
                if callbacks:
                    for callback in callbacks:
                        callback(epoch, train_metrics, val_metrics)
                
                # Early stopping
                if self.patience_counter >= self.config['patience']:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                    break
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        
        total_time = time.time() - start_time
        
        # Final metrics
        final_metrics = {
            'total_epochs': self.epoch + 1,
            'best_val_loss': self.best_val_loss,
            'total_time': total_time,
            'target_curve': self.target_curve,
            'training_history': self.training_history,
            'best_model_path': best_model_path
        }
        
        logger.info(f"Training completed in {total_time:.1f}s")
        logger.info(f"Best validation loss: {self.best_val_loss:.6f}")
        
        return final_metrics
    
    def _save_checkpoint(self, save_path: str, epoch: int, is_best: bool = False) -> str:
        """Save model checkpoint"""
        save_dir = Path(save_path)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Prepare checkpoint data
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'training_config': self.config,
            'target_curve': self.target_curve,
            'training_history': self.training_history,
            'model_config': self.model.get_config() if hasattr(self.model, 'get_config') else None
        }
        
        # Save checkpoint
        if is_best:
            checkpoint_path = save_dir / 'best_model.pth'
        else:
            checkpoint_path = save_dir / f'checkpoint_epoch_{epoch+1}.pth'
        
        torch.save(checkpoint, checkpoint_path)
        
        # Save training config and history as JSON for easy access
        if is_best:
            config_path = save_dir / 'training_config.json'
            history_path = save_dir / 'training_history.json'
            
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            
            with open(history_path, 'w') as f:
                json.dump(self.training_history, f, indent=2)
        
        logger.info(f"Checkpoint saved: {checkpoint_path}")
        return str(checkpoint_path)
    
    def load_checkpoint(self, checkpoint_path: str, load_optimizer: bool = True):
        """Load model checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # Load model state
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # Load training state
        if load_optimizer and 'optimizer_state_dict' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if load_optimizer and self.scheduler and 'scheduler_state_dict' in checkpoint:
            if checkpoint['scheduler_state_dict'] is not None:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        # Load training state
        self.epoch = checkpoint.get('epoch', 0)
        self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
        self.training_history = checkpoint.get('training_history', {
            'train_loss': [], 'val_loss': [], 'learning_rates': [], 'epoch_times': []
        })
        
        logger.info(f"Checkpoint loaded from {checkpoint_path}")
        logger.info(f"Resumed from epoch {self.epoch}, best val loss: {self.best_val_loss:.6f}")
    
    def evaluate_model(
        self, 
        test_dataset: Optional[GeneralWellLogDataset] = None,
        metrics: List[str] = ['mse', 'mae', 'r2']
    ) -> Dict[str, float]:
        """
        Evaluate model performance
        
        Args:
            test_dataset: Optional test dataset (uses validation if not provided)
            metrics: List of metrics to compute
            
        Returns:
            Dictionary of evaluation metrics
        """
        if test_dataset:
            test_loader = self._create_data_loader(test_dataset, shuffle=False)
        elif self.val_loader:
            test_loader = self.val_loader
        else:
            raise ValueError("No test or validation dataset available for evaluation")
        
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in test_loader:
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                predictions = self.model(inputs)
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        # Concatenate all predictions and targets
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        # Compute metrics
        evaluation_metrics = {}
        
        if 'mse' in metrics:
            evaluation_metrics['mse'] = np.mean((predictions - targets) ** 2)
        
        if 'mae' in metrics:
            evaluation_metrics['mae'] = np.mean(np.abs(predictions - targets))
        
        if 'rmse' in metrics:
            evaluation_metrics['rmse'] = np.sqrt(np.mean((predictions - targets) ** 2))
        
        if 'r2' in metrics:
            ss_res = np.sum((targets - predictions) ** 2)
            ss_tot = np.sum((targets - np.mean(targets)) ** 2)
            evaluation_metrics['r2'] = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0.0
        
        return evaluation_metrics
    
    def get_training_info(self) -> Dict[str, Any]:
        """Get comprehensive training information"""
        return {
            'target_curve': self.target_curve,
            'model_info': get_model_info(self.model),
            'training_config': self.config,
            'current_epoch': self.epoch,
            'best_val_loss': self.best_val_loss,
            'patience_counter': self.patience_counter,
            'device': str(self.device),
            'loss_function_info': self.loss_function.get_loss_info(),
            'dataset_info': {
                'train_samples': len(self.train_dataset),
                'val_samples': len(self.val_dataset) if self.val_dataset else 0,
                'input_curves': self.train_dataset.input_curves,
                'target_curve': self.train_dataset.target_curve
            }
        }


# Convenience functions for common training scenarios
def create_vp_trainer(
    model: GeneralWellLogTransformer,
    train_dataset: GeneralWellLogDataset,
    val_dataset: Optional[GeneralWellLogDataset] = None,
    custom_config: Optional[Dict[str, Any]] = None
) -> GeneralTrainingManager:
    """Create training manager optimized for Vp prediction"""
    vp_config = {
        'learning_rate': 1e-4,
        'batch_size': 8,
        'patience': 50,
        'loss_config': {'type': 'curve_specific'}
    }
    
    if custom_config:
        vp_config.update(custom_config)
    
    return GeneralTrainingManager(model, train_dataset, val_dataset, vp_config)


def create_density_trainer(
    model: GeneralWellLogTransformer,
    train_dataset: GeneralWellLogDataset,
    val_dataset: Optional[GeneralWellLogDataset] = None,
    custom_config: Optional[Dict[str, Any]] = None
) -> GeneralTrainingManager:
    """Create training manager optimized for density prediction"""
    density_config = {
        'learning_rate': 5e-5,
        'batch_size': 16,
        'patience': 30,
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {'constraint_weight': 0.5}
        }
    }
    
    if custom_config:
        density_config.update(custom_config)
    
    return GeneralTrainingManager(model, train_dataset, val_dataset, density_config)