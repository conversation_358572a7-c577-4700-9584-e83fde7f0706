"""
Train and Test VpTransformer Example
Demonstrates how to train the VpTransformer model and then test it with real data

This script shows:
1. How to train the VpTransformer using the built-in training function
2. How to load the trained model for inference
3. How to make predictions with trained weights
4. Comparison between untrained and trained model performance
"""
import os
import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import create_improved_vp_data, VpDataNormalizer, MWLT_Vp_Base, VpDataset, VpLoss
    from vp_predictor.utils import get_device, save_checkpoint
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    sys.exit(1)

def train_vp_model():
    """Train the VpTransformer model using proper training loop"""
    print("🏋️ TRAINING VpTransformer MODEL")
    print("="*60)
    print("This will create training data and train the model...")
    print("Note: This may take several minutes depending on your hardware")
    
    try:
        # Step 1: Create training data
        print("\n🔧 Creating training data...")
        input_data, target_data = create_improved_vp_data()
        
        if len(input_data) == 0:
            print("❌ No training data created. Check if A1.hdf5 and A2.hdf5 files exist.")
            return False
        
        print(f"✅ Created {len(input_data)} training samples")
        
        # Step 2: Split data into train/validation
        print("\n📊 Splitting data into train/validation sets...")
        X_train, X_val, y_train, y_val = train_test_split(
            input_data, target_data, test_size=0.2, random_state=42
        )
        
        print(f"   Training samples: {len(X_train)}")
        print(f"   Validation samples: {len(X_val)}")
        
        # Step 3: Create datasets and data loaders
        normalizer = VpDataNormalizer()
        
        train_dataset = VpDataset(X_train, y_train, normalizer, transform=True)
        val_dataset = VpDataset(X_val, y_val, normalizer, transform=False)
        
        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)
        
        # Step 4: Initialize model, loss, and optimizer
        print("\n🤖 Initializing model and training components...")
        device = get_device()
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(device)
        
        criterion = VpLoss(alpha=1.0, beta=0.1)
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=10, factor=0.5, min_lr=1e-7
        )
        
        # Step 5: Training loop
        print("\n🚀 Starting training loop...")
        num_epochs = 50
        best_val_loss = float('inf')
        patience = 15
        patience_counter = 0
        
        train_losses = []
        val_losses = []
        
        for epoch in range(num_epochs):
            # Training phase
            model.train()
            train_loss = 0.0
            num_batches = 0
            
            for batch_idx, (inputs, targets) in enumerate(train_loader):
                inputs, targets = inputs.to(device), targets.to(device)
                
                optimizer.zero_grad()
                outputs = model(inputs)
                
                # Normalize targets for loss calculation
                normalized_targets = normalizer.normalize_vp(targets.squeeze(1))
                loss = criterion(outputs, normalized_targets)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                num_batches += 1
                
                if batch_idx % 10 == 0:
                    print(f"   Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
            
            avg_train_loss = train_loss / num_batches
            train_losses.append(avg_train_loss)
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            num_val_batches = 0
            
            with torch.no_grad():
                for inputs, targets in val_loader:
                    inputs, targets = inputs.to(device), targets.to(device)
                    outputs = model(inputs)
                    
                    # Normalize targets for loss calculation
                    normalized_targets = normalizer.normalize_vp(targets.squeeze(1))
                    loss = criterion(outputs, normalized_targets)
                    
                    val_loss += loss.item()
                    num_val_batches += 1
            
            avg_val_loss = val_loss / num_val_batches
            val_losses.append(avg_val_loss)
            
            # Learning rate scheduling
            scheduler.step(avg_val_loss)
            
            print(f"   Epoch {epoch+1}/{num_epochs} - Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
            
            # Save best model
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                
                # Save checkpoint
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': best_val_loss,
                    'train_losses': train_losses,
                    'val_losses': val_losses
                }
                
                model_path = "../best_vp_model.pth"
                save_checkpoint(checkpoint, model_path)
                print(f"   ✅ New best model saved with validation loss: {best_val_loss:.4f}")
            else:
                patience_counter += 1
                
            # Early stopping
            if patience_counter >= patience:
                print(f"   ⏹️ Early stopping triggered after {patience} epochs without improvement")
                break
        
        print("✅ Training completed successfully!")
        print("📁 Check the generated files:")
        print("   - best_vp_model.pth (trained model)")
        print("   - Training logs and metrics")
        return True
        
    except Exception as e:
        print(f"❌ Training error: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_trained_model(model_path):
    """Load a trained VpTransformer model"""
    print(f"\n📂 Loading trained model from: {model_path}")
    
    try:
        device = get_device(device_id=0)
        
        # Create model architecture
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        
        # Load trained weights
        checkpoint = torch.load(model_path, map_location=device)
        
        # Handle different checkpoint formats
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"   📊 Epoch: {checkpoint.get('epoch', 'Unknown')}")
                print(f"   📉 Loss: {checkpoint.get('loss', 'Unknown')}")
            elif 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model.load_state_dict(checkpoint)
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        print("✅ Model loaded successfully!")
        return model, device
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None

def test_with_real_data(model, device, file_path, well_name):
    """Test the trained model with real well log data"""
    print(f"\n🧪 Testing trained model with {well_name}")
    print("-" * 40)
    
    try:
        # Load and prepare data
        curves = {}
        with h5py.File(file_path, 'r') as f:
            for curve_name in f.keys():
                data = f[curve_name][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data
        
        # Prepare input data
        input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        target_curve = 'AC'
        sequence_length = 640
        
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves:
                data = resample_curve(curves[curve_name], sequence_length)
                input_features.append(data)
        
        target_data = resample_curve(curves[target_curve], sequence_length)
        input_array = np.array(input_features)
        
        # Normalize data (important for trained model!)
        normalizer = VpDataNormalizer()
        
        # Normalize input curves
        normalized_input = []
        for i, curve_name in enumerate(input_curves):
            if curve_name in normalizer.input_stats:
                stats = normalizer.input_stats[curve_name]
                normalized = (input_array[i] - stats['mean']) / stats['std']
                normalized_input.append(normalized)
            else:
                print(f"⚠️  No normalization stats for {curve_name}")
                normalized_input.append(input_array[i])
        
        normalized_input = np.array(normalized_input)
        
        # Make prediction
        with torch.no_grad():
            input_tensor = torch.FloatTensor(normalized_input).unsqueeze(0).to(device)
            normalized_prediction = model(input_tensor)
            normalized_prediction_np = normalized_prediction.squeeze().cpu().numpy()
        
        # Denormalize prediction
        vp_stats = normalizer.vp_stats
        prediction = normalized_prediction_np * vp_stats['std'] + vp_stats['mean']
        
        print(f"   📊 Input shape: {input_array.shape}")
        print(f"   🎯 Target range: [{target_data.min():.1f}, {target_data.max():.1f}] μs/ft")
        print(f"   🔮 Prediction range: [{prediction.min():.1f}, {prediction.max():.1f}] μs/ft")
        
        # Calculate metrics
        error = prediction - target_data
        rmse = np.sqrt(np.mean(error**2))
        mae = np.mean(np.abs(error))
        r2 = 1 - np.sum(error**2) / np.sum((target_data - target_data.mean())**2)
        
        print(f"   📈 RMSE: {rmse:.2f} μs/ft")
        print(f"   📈 MAE: {mae:.2f} μs/ft")
        print(f"   📈 R²: {r2:.3f}")
        
        return {
            'input_data': input_array,
            'target_data': target_data,
            'prediction': prediction,
            'metrics': {'rmse': rmse, 'mae': mae, 'r2': r2}
        }
        
    except Exception as e:
        print(f"❌ Error testing with {well_name}: {e}")
        return None

def resample_curve(data, target_length):
    """Resample curve to target length"""
    if len(data) == target_length:
        return data
    original_indices = np.linspace(0, len(data) - 1, len(data))
    target_indices = np.linspace(0, len(data) - 1, target_length)
    return np.interp(target_indices, original_indices, data)

def find_trained_model():
    """Find the most recent trained model file"""
    # Look for common model file patterns
    possible_paths = [
        "../best_vp_model.pth",
        "../models/best_vp_model.pth",
        "../vp_model_best.pth",
        "../improved_vp_model.pth"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Look for any .pth files in parent directory
    parent_dir = ".."
    if os.path.exists(parent_dir):
        for file in os.listdir(parent_dir):
            if file.endswith('.pth') and 'vp' in file.lower():
                return os.path.join(parent_dir, file)
    
    return None

def main():
    """Main function demonstrating training and testing workflow"""
    print("🚀 TRAIN AND TEST VpTransformer EXAMPLE")
    print("="*60)
    print("Complete workflow: Train → Load → Test with real data")
    
    # Step 1: Check for existing trained model
    trained_model_path = find_trained_model()
    
    if trained_model_path:
        print(f"\n✅ Found existing trained model: {trained_model_path}")
        use_existing = input("Use existing model? (y/n): ").lower().strip()
        
        if use_existing != 'y':
            trained_model_path = None
    
    # Step 2: Train model if needed
    if not trained_model_path:
        print("\n🏋️ No trained model found. Starting training...")
        training_success = train_vp_model()
        
        if training_success:
            # Look for the newly created model
            trained_model_path = find_trained_model()
            if not trained_model_path:
                print("❌ Training completed but model file not found")
                return
        else:
            print("❌ Training failed. Cannot proceed with testing.")
            return
    
    # Step 3: Load trained model
    model, device = load_trained_model(trained_model_path)
    
    if model is None:
        print("❌ Failed to load model. Cannot proceed with testing.")
        return
    
    # Step 4: Test with real data
    test_files = [("A1.hdf5", "A1"), ("A2.hdf5", "A2")]
    results = {}
    
    for file_path, well_name in test_files:
        if os.path.exists(file_path):
            result = test_with_real_data(model, device, file_path, well_name)
            results[well_name] = result
        else:
            print(f"⚠️  Data file not found: {file_path}")
    
    # Step 5: Summary
    print(f"\n" + "="*60)
    print("🎉 TRAIN AND TEST COMPLETED")
    print("="*60)
    
    successful_tests = sum(1 for result in results.values() if result is not None)
    print(f"✅ Successfully tested: {successful_tests}/{len(test_files)} wells")
    
    for well_name, result in results.items():
        if result is not None:
            metrics = result['metrics']
            print(f"✅ {well_name}: RMSE={metrics['rmse']:.1f}, R²={metrics['r2']:.3f}")
        else:
            print(f"❌ {well_name}: Failed")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Analyze the prediction results")
    print(f"2. Fine-tune model hyperparameters if needed")
    print(f"3. Implement GeneralTransformer for multi-curve prediction")
    print(f"4. Deploy model for production use")

if __name__ == "__main__":
    main()