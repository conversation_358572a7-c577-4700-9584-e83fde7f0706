"""
Train and Test VpTransformer Example
Demonstrates how to train the VpTransformer model and then test it with real data

This script shows:
1. How to train the VpTransformer using the built-in training function
2. How to load the trained model for inference
3. How to make predictions with trained weights
4. Comparison between untrained and trained model performance
"""
import os
import sys
import time
import numpy as np
import h5py
import argparse
import torch
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split

# Plotting imports (matplotlib default; plotly optional)
import matplotlib
import matplotlib.pyplot as plt
try:
    import plotly.graph_objs as go
    from plotly.offline import plot as plotly_offline_plot
    _HAS_PLOTLY = True
except Exception:
    _HAS_PLOTLY = False

# Jupyter support
try:
    from IPython.display import display, clear_output
    _HAS_IPYTHON = True
except Exception:
    _HAS_IPYTHON = False

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import create_improved_vp_data, VpDataNormalizer, MWLT_Vp_Base, VpDataset, VpLoss
    from vp_predictor.utils import get_device, save_checkpoint
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    sys.exit(1)


# -------- Plotting helpers --------

def _is_notebook():
    try:
        from IPython import get_ipython  # type: ignore
        shell = get_ipython()
        if not shell:
            return False
        return 'IPKernelApp' in shell.config
    except Exception:
        return False


class LiveTrainingPlotter:
    """Lightweight live plotter for training curves using matplotlib.
    Works in notebooks (clears output) and in standalone (interactive window).
    """
    def __init__(self, enable=True, save_dir=None, save_interval=5):
        self.enable = bool(enable)
        self.save_dir = save_dir
        self.save_interval = int(save_interval or 0)
        self.fig = None
        self.ax_loss = None
        self.ax_lr = None
        self._notebook = _is_notebook() and _HAS_IPYTHON
        if self.enable:
            plt.ion()
            self._init_fig()

    def _init_fig(self):
        self.fig, (self.ax_loss, self.ax_lr) = plt.subplots(1, 2, figsize=(12, 4))
        self.ax_loss.set_title('Training/Validation Loss')
        self.ax_loss.set_xlabel('Epoch')
        self.ax_loss.set_ylabel('Loss')
        self.ax_loss.grid(True, alpha=0.3)

        self.ax_lr.set_title('Learning Rate')
        self.ax_lr.set_xlabel('Epoch')
        self.ax_lr.set_ylabel('LR')
        self.ax_lr.grid(True, alpha=0.3)
        self.fig.tight_layout()
        try:
            self.fig.canvas.manager.set_window_title('MWLT Training Progress')
        except Exception:
            pass

    def update(self, epoch, train_losses, val_losses, lrs=None):
        if not self.enable:
            return
        self.ax_loss.cla(); self.ax_lr.cla()
        # Re-draw axes decorations
        self.ax_loss.set_title('Training/Validation Loss')
        self.ax_loss.set_xlabel('Epoch')
        self.ax_loss.set_ylabel('Loss')
        self.ax_loss.grid(True, alpha=0.3)
        self.ax_lr.set_title('Learning Rate')
        self.ax_lr.set_xlabel('Epoch')
        self.ax_lr.set_ylabel('LR')
        self.ax_lr.grid(True, alpha=0.3)

        epochs = np.arange(1, len(train_losses) + 1)
        self.ax_loss.plot(epochs, train_losses, label='Train', color='tab:blue')
        if len(val_losses) == len(train_losses):
            self.ax_loss.plot(epochs, val_losses, label='Val', color='tab:orange')
        elif len(val_losses) > 0:
            self.ax_loss.plot(np.arange(1, len(val_losses) + 1), val_losses, label='Val', color='tab:orange')
        self.ax_loss.legend()

        if lrs is not None and len(lrs) > 0:
            self.ax_lr.plot(np.arange(1, len(lrs) + 1), lrs, color='tab:green')

        self.fig.tight_layout()
        if self._notebook:
            try:
                clear_output(wait=True)  # type: ignore
                display(self.fig)  # type: ignore
            except Exception:
                pass
        else:
            self.fig.canvas.draw_idle()
            try:
                self.fig.canvas.flush_events()
            except Exception:
                pass
            plt.pause(0.001)

        # Auto-save periodically
        if self.save_dir and self.save_interval and (epoch + 1) % self.save_interval == 0:
            os.makedirs(self.save_dir, exist_ok=True)
            self.fig.savefig(os.path.join(self.save_dir, 'training_curves.png'), dpi=150)

    def save_final(self, path):
        if self.enable and self.fig is not None and path:
            os.makedirs(os.path.dirname(path), exist_ok=True)
            self.fig.savefig(path, dpi=150)

    def close(self):
        if self.enable and self.fig is not None:
            try:
                plt.ioff()
                plt.close(self.fig)
            except Exception:
                pass


# Default plotting options (used when CLI flags are not provided)
DEFAULT_PLOT_OPTIONS = {
    'enable': True,                     # master switch
    'show_during_training': True,       # live updating during training
    'save': True,                       # save plots to disk
    'save_dir': os.path.join(os.path.dirname(__file__), 'prediction_outputs'),
    'save_interval': 5,                 # epochs between autosaves for training curves
    'prediction_backend': 'matplotlib', # 'matplotlib' or 'plotly'
}

def train_vp_model(plot_options: dict | None = None):
    """Train the VpTransformer model using proper training loop with optional live plotting.

    Args:
        plot_options: dict with keys similar to DEFAULT_PLOT_OPTIONS. If None, defaults are used.
    """
    print("🏋️ TRAINING VpTransformer MODEL")
    print("="*60)
    print("This will create training data and train the model...")
    print("Note: This may take several minutes depending on your hardware")

    # Resolve plotting config
    opts = {**DEFAULT_PLOT_OPTIONS, **(plot_options or {})}
    live_plot = LiveTrainingPlotter(
        enable=opts.get('enable') and opts.get('show_during_training'),
        save_dir=opts.get('save_dir') if opts.get('save') else None,
        save_interval=opts.get('save_interval', 5),
    )

    try:
        # Step 1: Create training data
        print("\n🔧 Creating training data...")
        input_data, target_data = create_improved_vp_data()

        if len(input_data) == 0:
            print("❌ No training data created. Check if A1.hdf5 and A2.hdf5 files exist.")
            return False

        print(f"✅ Created {len(input_data)} training samples")

        # Step 2: Split data into train/validation
        print("\n📊 Splitting data into train/validation sets...")
        X_train, X_val, y_train, y_val = train_test_split(
            input_data, target_data, test_size=0.2, random_state=42
        )

        print(f"   Training samples: {len(X_train)}")
        print(f"   Validation samples: {len(X_val)}")

        # Step 3: Create datasets and data loaders
        normalizer = VpDataNormalizer()

        train_dataset = VpDataset(X_train, y_train, normalizer, transform=True)
        val_dataset = VpDataset(X_val, y_val, normalizer, transform=False)

        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)

        # Step 4: Initialize model, loss, and optimizer
        print("\n🤖 Initializing model and training components...")
        device = get_device()
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(device)

        criterion = VpLoss(alpha=1.0, beta=0.1)
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=10, factor=0.5, min_lr=1e-7
        )

        # Step 5: Training loop
        print("\n🚀 Starting training loop...")
        num_epochs = 50
        best_val_loss = float('inf')
        patience = 15
        patience_counter = 0

        train_losses = []
        val_losses = []
        lrs = []

        for epoch in range(num_epochs):
            # Training phase
            model.train()
            train_loss = 0.0
            num_batches = 0

            for batch_idx, (inputs, targets) in enumerate(train_loader):
                inputs, targets = inputs.to(device), targets.to(device)

                optimizer.zero_grad()
                outputs = model(inputs)

                # Normalize targets for loss calculation
                normalized_targets = normalizer.normalize_vp(targets.squeeze(1))
                loss = criterion(outputs, normalized_targets)

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()

                train_loss += loss.item()
                num_batches += 1

                if batch_idx % 10 == 0:
                    print(f"   Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")

            avg_train_loss = train_loss / max(1, num_batches)
            train_losses.append(avg_train_loss)

            # Validation phase
            model.eval()
            val_loss = 0.0
            num_val_batches = 0

            with torch.no_grad():
                for inputs, targets in enumerate(val_loader):
                    # The original loop is (inputs, targets) in val_loader; fix typo here
                    break
            # Correct validation loop
            model.eval()
            val_loss = 0.0
            num_val_batches = 0
            with torch.no_grad():
                for inputs, targets in val_loader:
                    inputs, targets = inputs.to(device), targets.to(device)
                    outputs = model(inputs)
                    normalized_targets = normalizer.normalize_vp(targets.squeeze(1))
                    loss = criterion(outputs, normalized_targets)
                    val_loss += loss.item()
                    num_val_batches += 1

            avg_val_loss = val_loss / max(1, num_val_batches)
            val_losses.append(avg_val_loss)

            # Learning rate scheduling and track LR
            scheduler.step(avg_val_loss)
            lrs.append(optimizer.param_groups[0]['lr'])

            print(f"   Epoch {epoch+1}/{num_epochs} - Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")

            # Update live plot
            try:
                live_plot.update(epoch, train_losses, val_losses, lrs)
            except Exception:
                pass

            # Save best model
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0

                # Save checkpoint
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': best_val_loss,
                    'train_losses': train_losses,
                    'val_losses': val_losses,
                }

                model_path = "../best_vp_model.pth"
                save_checkpoint(checkpoint, model_path)
                print(f"   ✅ New best model saved with validation loss: {best_val_loss:.4f}")
            else:
                patience_counter += 1

            # Early stopping
            if patience_counter >= patience:
                print(f"   ⏹️ Early stopping triggered after {patience} epochs without improvement")
                break

        # Save final training curves if requested
        if opts.get('save') and opts.get('enable'):
            live_plot.save_final(os.path.join(opts['save_dir'], 'training_curves_final.png'))
        live_plot.close()

        print("✅ Training completed successfully!")
        print("📁 Check the generated files:")
        print("   - best_vp_model.pth (trained model)")
        print("   - Training logs and metrics")
        return True

    except Exception as e:
        try:
            live_plot.close()
        except Exception:
            pass
        print(f"❌ Training error: {e}")
        import traceback
        traceback.print_exc()
        return False

def load_trained_model(model_path):
    """Load a trained VpTransformer model"""
    print(f"\n📂 Loading trained model from: {model_path}")

    try:
        device = get_device(device_id=0)

        # Create model architecture
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)

        # Load trained weights
        checkpoint = torch.load(model_path, map_location=device)

        # Handle different checkpoint formats
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"   📊 Epoch: {checkpoint.get('epoch', 'Unknown')}")
                print(f"   📉 Loss: {checkpoint.get('loss', 'Unknown')}")
            elif 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model.load_state_dict(checkpoint)
        else:
            model.load_state_dict(checkpoint)

        model = model.to(device)
        model.eval()

        print("✅ Model loaded successfully!")
        return model, device

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None

def test_with_real_data(model, device, file_path, well_name):
    """Test the trained model with real well log data"""
    print(f"\n🧪 Testing trained model with {well_name}")
    print("-" * 40)

    try:
        # Load and prepare data
        curves = {}
        with h5py.File(file_path, 'r') as f:
            for curve_name in f.keys():
                data = f[curve_name][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data

        # Prepare input data
        input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        target_curve = 'AC'
        sequence_length = 640

        input_features = []
        for curve_name in input_curves:
            if curve_name in curves:
                data = resample_curve(curves[curve_name], sequence_length)
                input_features.append(data)

        target_data = resample_curve(curves[target_curve], sequence_length)
        input_array = np.array(input_features)

        # Normalize data (important for trained model!)
        normalizer = VpDataNormalizer()

        # Normalize input curves
        normalized_input = []
        for i, curve_name in enumerate(input_curves):
            if curve_name in normalizer.input_stats:
                stats = normalizer.input_stats[curve_name]
                normalized = (input_array[i] - stats['mean']) / stats['std']
                normalized_input.append(normalized)
            else:
                print(f"⚠️  No normalization stats for {curve_name}")
                normalized_input.append(input_array[i])

        normalized_input = np.array(normalized_input)

        # Make prediction
        with torch.no_grad():
            input_tensor = torch.FloatTensor(normalized_input).unsqueeze(0).to(device)
            normalized_prediction = model(input_tensor)
            normalized_prediction_np = normalized_prediction.squeeze().cpu().numpy()

        # Denormalize prediction
        vp_stats = normalizer.vp_stats
        prediction = normalized_prediction_np * vp_stats['std'] + vp_stats['mean']

        print(f"   📊 Input shape: {input_array.shape}")
        print(f"   🎯 Target range: [{target_data.min():.1f}, {target_data.max():.1f}] μs/ft")
        print(f"   🔮 Prediction range: [{prediction.min():.1f}, {prediction.max():.1f}] μs/ft")

        # Calculate metrics
        error = prediction - target_data
        rmse = np.sqrt(np.mean(error**2))
        mae = np.mean(np.abs(error))
        r2 = 1 - np.sum(error**2) / np.sum((target_data - target_data.mean())**2)

        print(f"   📈 RMSE: {rmse:.2f} μs/ft")
        print(f"   📈 MAE: {mae:.2f} μs/ft")
        print(f"   📈 R²: {r2:.3f}")

        return {
            'input_data': input_array,
            'target_data': target_data,
            'prediction': prediction,
            'metrics': {'rmse': rmse, 'mae': mae, 'r2': r2}
        }

    except Exception as e:
        print(f"❌ Error testing with {well_name}: {e}")
        return None

def resample_curve(data, target_length):
    """Resample curve to target length"""
    if len(data) == target_length:
        return data
    original_indices = np.linspace(0, len(data) - 1, len(data))
    target_indices = np.linspace(0, len(data) - 1, target_length)
    return np.interp(target_indices, original_indices, data)

def plot_prediction_results(well_name, target_data, prediction, output_dir, show=False, backend: str = 'matplotlib', auto_save=True):
    """Create and save interactive plots comparing prediction vs target for a well.

    - backend: 'matplotlib' (default) or 'plotly' (if installed)
    - show: whether to display interactively
    - auto_save: save PNG (matplotlib) or HTML (plotly)
    """
    os.makedirs(output_dir, exist_ok=True)

    # Common x-axis is sample index
    x = np.arange(len(target_data))

    if backend == 'plotly' and _HAS_PLOTLY:
        traces = [
            go.Scatter(x=x, y=target_data, mode='lines', name='Target (AC)', line=dict(color='black', width=1)),
            go.Scatter(x=x, y=prediction, mode='lines', name='Prediction', line=dict(color='royalblue')),
        ]
        layout1 = go.Layout(title=f"{well_name}: Target vs Prediction", xaxis_title="Sample index", yaxis_title="Vp (μs/ft)")
        fig1 = go.Figure(data=traces, layout=layout1)

        min_v = float(min(np.min(target_data), np.min(prediction)))
        max_v = float(max(np.max(target_data), np.max(prediction)))
        fig2 = go.Figure()
        fig2.add_trace(go.Scatter(x=target_data, y=prediction, mode='markers', name='Points', opacity=0.6))
        fig2.add_trace(go.Scatter(x=[min_v, max_v], y=[min_v, max_v], mode='lines', name='y=x', line=dict(dash='dash', color='red')))
        fig2.update_layout(title="Target vs Prediction (scatter)", xaxis_title="Target (μs/ft)", yaxis_title="Prediction (μs/ft)")

        residuals = prediction - target_data
        fig3 = go.Figure(data=[go.Histogram(x=residuals, nbinsx=40, name='Residuals')])
        fig3.update_layout(title="Residuals (Pred - Target)", xaxis_title="Residual (μs/ft)", yaxis_title="Count")

        if auto_save:
            html_path = os.path.join(output_dir, f"{well_name}_prediction_results.html")
            try:
                fig1.write_html(html_path.replace('.html', '_line.html'), include_plotlyjs='cdn')
                fig2.write_html(html_path.replace('.html', '_scatter.html'), include_plotlyjs='cdn')
                fig3.write_html(html_path.replace('.html', '_residuals.html'), include_plotlyjs='cdn')
                print(f"💾 Saved interactive HTML plots: {html_path.replace('.html', '_*.html')}")
            except Exception as _:
                pass
        if show:
            try:
                fig1.show(); fig2.show(); fig3.show()
            except Exception:
                pass
        return

    # Matplotlib fallback (or default)
    fig, axes = plt.subplots(1, 3, figsize=(18, 4))

    # 1) Line overlay
    axes[0].plot(x, target_data, label='Target (AC)', color='black', linewidth=1)
    axes[0].plot(x, prediction, label='Prediction', color='tab:blue', alpha=0.8)
    axes[0].set_title(f"{well_name}: Target vs Prediction")
    axes[0].set_xlabel("Sample index")
    axes[0].set_ylabel("Vp (μs/ft)")
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 2) Scatter with y=x
    min_v = float(min(np.min(target_data), np.min(prediction)))
    max_v = float(max(np.max(target_data), np.max(prediction)))
    axes[1].scatter(target_data, prediction, s=8, alpha=0.6)
    axes[1].plot([min_v, max_v], [min_v, max_v], 'r--', linewidth=1)
    axes[1].set_title("Target vs Prediction (scatter)")
    axes[1].set_xlabel("Target (μs/ft)")
    axes[1].set_ylabel("Prediction (μs/ft)")
    axes[1].grid(True, alpha=0.3)

    # 3) Residuals histogram
    residuals = prediction - target_data
    axes[2].hist(residuals, bins=40, color='tab:orange', alpha=0.8)
    axes[2].axvline(0.0, color='k', linestyle='--', linewidth=1)
    axes[2].set_title("Residuals (Pred - Target)")
    axes[2].set_xlabel("Residual (μs/ft)")
    axes[2].set_ylabel("Count")
    axes[2].grid(True, alpha=0.3)

    fig.tight_layout()
    if auto_save:
        save_path = os.path.join(output_dir, f"{well_name}_prediction_results.png")
        fig.savefig(save_path, dpi=150)
        print(f"💾 Saved plot: {save_path}")
    if show:
        plt.show()
    else:
        plt.close(fig)


def save_results_csv(well_name, target_data, prediction, output_dir):
    """Save target, prediction, and residuals to a CSV for deeper analysis."""
    os.makedirs(output_dir, exist_ok=True)
    residuals = prediction - target_data
    csv_path = os.path.join(output_dir, f"{well_name}_prediction_results.csv")
    try:
        with open(csv_path, 'w') as f:
            f.write("index,target,prediction,residual\n")
            for i, (t, p, r) in enumerate(zip(target_data, prediction, residuals)):
                f.write(f"{i},{t:.6f},{p:.6f},{r:.6f}\n")
        print(f"💾 Saved CSV: {csv_path}")
    except Exception as e:
        print(f"⚠️ Failed to save CSV {csv_path}: {e}")


def find_trained_model():
    """Find the most recent trained model file"""
    # Look for common model file patterns
    possible_paths = [
        "../best_vp_model.pth",
        "../models/best_vp_model.pth",
        "../vp_model_best.pth",
        "../improved_vp_model.pth"
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # Look for any .pth files in parent directory
    parent_dir = ".."
    if os.path.exists(parent_dir):
        for file in os.listdir(parent_dir):
            if file.endswith('.pth') and 'vp' in file.lower():
                return os.path.join(parent_dir, file)

    return None

def main(plot: bool | None = None,
         plot_backend: str | None = None,
         auto_save: bool | None = None,
         save_dir: str | None = None):
    """Main function demonstrating training and testing workflow with optional interactive plotting.

    Args:
        plot: master switch for plotting (defaults to True)
        plot_backend: 'matplotlib' or 'plotly' (if installed); defaults to 'matplotlib'
        auto_save: whether to save plots automatically (defaults to True)
        save_dir: directory to save plots (defaults to examples/prediction_outputs)
    """
    print("🚀 TRAIN AND TEST VpTransformer EXAMPLE")
    print("="*60)
    print("Complete workflow: Train → Load → Test with real data")

    # Backward compatibility: still accept CLI but no longer required
    parser = argparse.ArgumentParser(description="Train, test, and visualize VpTransformer results", add_help=False)
    parser.add_argument("--show-plots", action="store_true", help="Display plots in addition to saving")
    parser.add_argument("--plot-backend", type=str, choices=["matplotlib", "plotly"], help="Visualization backend")
    parser.add_argument("--no-save", action="store_true", help="Do not save plots to disk")
    parser.add_argument("--save-dir", type=str, help="Directory to save plots")
    known_args, _ = parser.parse_known_args()

    # Resolve plotting options (defaults make plotting easy without flags)
    enable_plotting = DEFAULT_PLOT_OPTIONS['enable'] if plot is None else bool(plot)
    backend = (plot_backend or known_args.plot_backend or DEFAULT_PLOT_OPTIONS['prediction_backend']).lower()
    do_save = (DEFAULT_PLOT_OPTIONS['save'] if auto_save is None else bool(auto_save)) and (not known_args.no_save)
    out_dir = save_dir or known_args.save_dir or DEFAULT_PLOT_OPTIONS['save_dir']

    # Step 1: Check for existing trained model
    trained_model_path = find_trained_model()

    if trained_model_path:
        print(f"\n✅ Found existing trained model: {trained_model_path}")
        use_existing = input("Use existing model? (y/n): ").lower().strip()
        if use_existing != 'y':
            trained_model_path = None

    # Step 2: Train model if needed
    if not trained_model_path:
        print("\n🏋️ No trained model found. Starting training...")
        training_success = train_vp_model({
            'enable': enable_plotting,
            'show_during_training': enable_plotting,  # live updates
            'save': do_save,
            'save_dir': out_dir,
            'save_interval': DEFAULT_PLOT_OPTIONS['save_interval'],
        })

        if training_success:
            # Look for the newly created model
            trained_model_path = find_trained_model()
            if not trained_model_path:
                print("❌ Training completed but model file not found")
                return
        else:
            print("❌ Training failed. Cannot proceed with testing.")
            return

    # Step 3: Load trained model
    model, device = load_trained_model(trained_model_path)

    if model is None:
        print("❌ Failed to load model. Cannot proceed with testing.")
        return

    # Step 4: Test with real data
    # Always resolve data files relative to this examples/ directory
    script_dir = os.path.dirname(__file__)
    test_files = [
        (os.path.join(script_dir, "A1.hdf5"), "A1"),
        (os.path.join(script_dir, "A2.hdf5"), "A2"),
    ]
    results = {}

    for file_path, well_name in test_files:
        if os.path.exists(file_path):
            result = test_with_real_data(model, device, file_path, well_name)
            results[well_name] = result
        else:
            print(f"⚠️  Data file not found: {file_path}")

    # Step 5: Summary
    print(f"\n" + "="*60)
    print("🎉 TRAIN AND TEST COMPLETED")
    print("="*60)

    successful_tests = sum(1 for result in results.values() if result is not None)
    print(f"✅ Successfully tested: {successful_tests}/{len(test_files)} wells")

    for well_name, result in results.items():
        if result is not None:
            metrics = result['metrics']
            print(f"✅ {well_name}: RMSE={metrics['rmse']:.1f}, R²={metrics['r2']:.3f}")
        else:
            print(f"❌ {well_name}: Failed")

    # Step 6: Plot and save results
    output_dir = out_dir
    for well_name, result in results.items():
        if result is None:
            continue
        target = result['target_data']
        pred = result['prediction']
        plot_prediction_results(
            well_name, target, pred, output_dir,
            show=enable_plotting, backend=backend, auto_save=do_save
        )
        save_results_csv(well_name, target, pred, output_dir)

    print(f"\n📋 NEXT STEPS:")
    print(f"1. Analyze the prediction results")
    print(f"2. Fine-tune model hyperparameters if needed")
    print(f"3. Implement GeneralTransformer for multi-curve prediction")
    print(f"4. Deploy model for production use")


# Entry point: defaults enable plotting without CLI flags

if __name__ == "__main__":
    main()