"""
Quick integration test for Phase 1+2 components
"""

import sys
import os
import numpy as np
import torch
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_dummy_data():
    """Create dummy well log data for testing"""
    num_samples = 1000
    seq_len = 720
    
    # Create realistic well log curves
    data_dict = {}
    
    # Gamma Ray: 20-150 API units
    data_dict['GR'] = np.random.uniform(20, 150, num_samples * seq_len)
    
    # Neutron: 0-40%
    data_dict['CNL'] = np.random.uniform(0, 40, num_samples * seq_len)
    
    # Density: 2.0-2.8 g/cm3
    data_dict['DEN'] = np.random.uniform(2.0, 2.8, num_samples * seq_len)
    
    # Resistivity: 1-100 ohm-m (log-distributed)
    data_dict['RLLD'] = np.random.lognormal(1, 1, num_samples * seq_len)
    
    # Acoustic/VP: 60-120 us/ft (correlated with porosity)
    porosity_factor = (data_dict['CNL'] / 40.0)  # Normalized porosity
    data_dict['VP'] = 60 + porosity_factor * 60 + np.random.normal(0, 5, num_samples * seq_len)
    data_dict['VP'] = np.clip(data_dict['VP'], 40, 400)  # Clip to physical range
    data_dict['AC'] = data_dict['VP'].copy()  # Also create AC for compatibility
    
    return data_dict

def test_integration():
    """Test full integration of Phase 1 and Phase 2 components"""
    logger.info("=== Testing Phase 1 + Phase 2 Integration ===")
    
    try:
        from vp_predictor import (
            # Phase 1 components
            GeneralWellLogTransformer, GeneralDataNormalizer,
            get_model_template,
            
            # Phase 2 components
            GeneralWellLogDataset, GeneralTrainingManager,
            get_training_template, get_data_template
        )
        
        # Create complete training pipeline
        logger.info("Setting up complete training pipeline...")
        
        # 1. Get configurations
        model_config = get_model_template('vp_prediction')
        training_config = get_training_template('fast_vp_training')
        training_config['max_epochs'] = 1  # Single epoch for testing
        training_config['batch_size'] = 2
        data_config = get_data_template('standard_sequence')
        
        # 2. Create data (smaller for quick test)
        logger.info("Creating test data...")
        data_dict = create_dummy_data()
        
        # Use just a subset of data for quick test
        subset_size = 2000  # Much smaller
        for key in data_dict:
            data_dict[key] = data_dict[key][:subset_size]
        
        # 3. Create components
        logger.info("Creating model and normalizer...")
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        normalizer = GeneralDataNormalizer(
            model_config['input_curves'], 
            model_config['output_curves']
        )
        
        logger.info("Creating dataset...")
        dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],  # Single target
            normalizer=normalizer,
            sequence_config=data_config
        )
        
        logger.info(f"Dataset created with {len(dataset)} samples")
        
        # Test dataset sample
        inputs, targets = dataset[0]
        logger.info(f"Sample shapes - inputs: {inputs.shape}, targets: {targets.shape}")
        
        # 4. Create trainer
        logger.info("Creating trainer...")
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=dataset,
            val_dataset=None,  # No validation for quick test
            training_config=training_config
        )
        
        logger.info("✓ Complete pipeline created successfully")
        
        # Test that all components work together
        info = trainer.get_training_info()
        assert info['target_curve'] == 'VP'  # Fixed: should be VP, not AC
        assert len(info['dataset_info']['input_curves']) == 4
        
        logger.info("✓ Training info retrieval works")
        logger.info(f"  Target curve: {info['target_curve']}")
        logger.info(f"  Input curves: {info['dataset_info']['input_curves']}")
        logger.info(f"  Training samples: {info['dataset_info']['train_samples']}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Add current directory to Python path for imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    success = test_integration()
    if success:
        print("\n🎉 Phase 1+2 Integration Test PASSED!")
    else:
        print("\n❌ Phase 1+2 Integration Test FAILED!")
    
    sys.exit(0 if success else 1)