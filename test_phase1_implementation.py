#!/usr/bin/env python3
"""
Phase 1 Implementation Test Suite

Tests the Phase 1 refactoring implementation to ensure:
1. Backward compatibility with existing VpTransformer code
2. New GeneralWellLogTransformer functionality works
3. Configuration system operates correctly
4. API interfaces function properly
"""

import sys
import os
import torch
import numpy as np

# Add the vp_predictor package to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'vp_predictor'))

def test_imports():
    """Test that all imports work correctly"""
    print("=" * 60)
    print("Testing Phase 1 Imports")
    print("=" * 60)
    
    try:
        # Test backward compatible imports
        from vp_predictor import (
            VpTransformer, VpDecoder, VpDataNormalizer,
            MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large,
            get_model, get_normalizer
        )
        print("✅ Backward compatible imports successful")
        
        # Test new general imports
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDecoder, GeneralDataNormalizer,
            GWLT_VpPrediction, GWLT_MultiCurve,
            get_general_model, create_custom_model
        )
        print("✅ New general transformer imports successful")
        
        # Test configuration imports
        from vp_predictor import (
            CURVE_CONFIGURATIONS, MODEL_TEMPLATES,
            get_curve_config, get_model_template
        )
        print("✅ Configuration system imports successful")
        
        # Test API imports
        from vp_predictor import GeneralWellLogPredictor
        print("✅ API imports successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_backward_compatibility():
    """Test that existing VpTransformer code still works"""
    print("\n" + "=" * 60)
    print("Testing Backward Compatibility")
    print("=" * 60)
    
    try:
        from vp_predictor import get_model, get_normalizer
        
        # Test original model creation
        model = get_model('base')
        print(f"✅ Created VpTransformer model: {type(model).__name__}")
        
        # Test model forward pass
        dummy_input = torch.randn(1, 4, 640)  # [B, C, L]
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✅ Model forward pass: {dummy_input.shape} -> {output.shape}")
        
        # Test normalizer
        normalizer = get_normalizer()
        dummy_curves = {
            'GR': torch.randn(640),
            'CNL': torch.randn(640),
            'DEN': torch.randn(640),
            'RLLD': torch.randn(640)
        }
        
        normalized = normalizer.normalize_inputs(dummy_curves)
        print(f"✅ Normalizer works: {len(normalized)} curves normalized")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return False

def test_configuration_system():
    """Test the new configuration system"""
    print("\n" + "=" * 60)
    print("Testing Configuration System")
    print("=" * 60)
    
    try:
        from vp_predictor import get_curve_config, get_model_template, CURVE_CONFIGURATIONS
        
        # Test curve configuration
        gr_config = get_curve_config('GR')
        print(f"✅ GR configuration: {gr_config['name']} ({gr_config['unit']})")
        
        vp_config = get_curve_config('VP')
        print(f"✅ VP configuration: {vp_config['name']} ({vp_config['unit']})")
        
        # Test model template
        vp_template = get_model_template('vp_prediction')
        print(f"✅ Vp prediction template: {vp_template['input_curves']} -> {vp_template['output_curves']}")
        
        # Test multi-curve template
        multi_template = get_model_template('multi_curve_basic')
        print(f"✅ Multi-curve template: {multi_template['input_curves']} -> {multi_template['output_curves']}")
        
        # Test curve count
        print(f"✅ Total supported curves: {len(CURVE_CONFIGURATIONS)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration system test failed: {e}")
        return False

def test_general_transformer():
    """Test the new GeneralWellLogTransformer"""
    print("\n" + "=" * 60)
    print("Testing GeneralWellLogTransformer")
    print("=" * 60)
    
    try:
        from vp_predictor import GeneralWellLogTransformer, get_general_model
        
        # Test template-based creation
        vp_model = get_general_model('vp_prediction')
        print(f"✅ Created Vp transformer: {type(vp_model).__name__}")
        
        # Test forward pass
        dummy_input = torch.randn(1, 4, 640)
        with torch.no_grad():
            output = vp_model(dummy_input)
        print(f"✅ Vp model forward pass: {dummy_input.shape} -> {output.shape}")
        
        # Test multi-curve model creation
        multi_model = get_general_model('multi_curve_basic')
        print(f"✅ Created multi-curve transformer: {type(multi_model).__name__}")
        
        # Test multi-curve forward pass
        dummy_input_2ch = torch.randn(1, 2, 640)  # 2 input curves
        with torch.no_grad():
            multi_output = multi_model(dummy_input_2ch)
        print(f"✅ Multi-curve forward pass: {dummy_input_2ch.shape} -> output type: {type(multi_output)}")
        
        return True
        
    except Exception as e:
        print(f"❌ General transformer test failed: {e}")
        return False

def test_general_normalizer():
    """Test the new GeneralDataNormalizer"""
    print("\n" + "=" * 60)
    print("Testing GeneralDataNormalizer")
    print("=" * 60)
    
    try:
        from vp_predictor import GeneralDataNormalizer
        
        # Test Vp-compatible normalizer
        vp_normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            output_curves=['VP']
        )
        
        dummy_curves = {
            'GR': torch.randn(640),
            'CNL': torch.randn(640),
            'DEN': torch.randn(640),
            'RLLD': torch.randn(640)
        }
        
        normalized = vp_normalizer.normalize_inputs(dummy_curves)
        print(f"✅ Vp normalizer: {len(normalized)} input curves normalized")
        
        # Test multi-curve normalizer
        multi_normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'CNL'],
            output_curves=['DEN', 'AC', 'RLLD']
        )
        
        dummy_targets = {
            'DEN': torch.randn(640),
            'AC': torch.randn(640),
            'RLLD': torch.randn(640)
        }
        
        normalized_targets = multi_normalizer.normalize_targets(dummy_targets)
        print(f"✅ Multi-curve normalizer: {len(normalized_targets)} target curves normalized")
        
        # Test denormalization
        denormalized = multi_normalizer.denormalize_predictions(normalized_targets)
        print(f"✅ Denormalization: {len(denormalized)} curves denormalized")
        
        return True
        
    except Exception as e:
        print(f"❌ General normalizer test failed: {e}")
        return False

def test_custom_model_creation():
    """Test creating custom models with arbitrary curve combinations"""
    print("\n" + "=" * 60)
    print("Testing Custom Model Creation")
    print("=" * 60)
    
    try:
        from vp_predictor import create_custom_model
        
        # Test custom curve combination
        custom_model = create_custom_model(
            input_curves=['GR', 'DEN'],
            output_curves=['AC'],
            model_size='small'
        )
        
        print(f"✅ Created custom model: GR+DEN -> AC")
        
        # Test forward pass
        dummy_input = torch.randn(1, 2, 640)  # 2 input curves
        with torch.no_grad():
            output = custom_model(dummy_input)
        print(f"✅ Custom model forward pass: {dummy_input.shape} -> {output.shape}")
        
        # Test model info
        model_info = custom_model.get_model_info()
        print(f"✅ Model info: {model_info['input_curves']} -> {model_info['output_curves']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Custom model creation test failed: {e}")
        return False

def test_api_compatibility():
    """Test that API interfaces work"""
    print("\n" + "=" * 60)
    print("Testing API Compatibility")  
    print("=" * 60)
    
    try:
        from vp_predictor import GeneralWellLogPredictor
        
        # Test creating predictor from template
        predictor = GeneralWellLogPredictor(template_name='vp_prediction')
        print("✅ Created GeneralWellLogPredictor from template")
        
        # Test supported curves
        supported = predictor.get_supported_curves()
        print(f"✅ Supported curves: {supported['input_curves']} -> {supported['output_curves']}")
        
        # Test prediction interface (without actual model weights)
        dummy_curves = {
            'GR': np.random.randn(640),
            'CNL': np.random.randn(640),
            'DEN': np.random.randn(640),
            'RLLD': np.random.randn(640)
        }
        
        try:
            # This will work but give random results (no trained weights)
            result = predictor.predict_curves(dummy_curves)
            print(f"✅ Prediction interface works: {list(result.keys())}")
        except Exception as e:
            print(f"⚠️ Prediction test skipped (no trained weights): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API compatibility test failed: {e}")
        return False

def run_all_tests():
    """Run all Phase 1 tests"""
    print("🚀 Starting Phase 1 Implementation Tests")
    print("Testing VpTransformer -> General Well Log Transformer refactoring")
    
    tests = [
        ("Import Tests", test_imports),
        ("Backward Compatibility", test_backward_compatibility),
        ("Configuration System", test_configuration_system),
        ("General Transformer", test_general_transformer),
        ("General Normalizer", test_general_normalizer),
        ("Custom Model Creation", test_custom_model_creation),
        ("API Compatibility", test_api_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("PHASE 1 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests Passed: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL PHASE 1 TESTS PASSED!")
        print("✅ Refactoring implementation successful")
        print("✅ Backward compatibility maintained")
        print("✅ New functionality working")
    else:
        print("⚠️ Some tests failed - review implementation")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)